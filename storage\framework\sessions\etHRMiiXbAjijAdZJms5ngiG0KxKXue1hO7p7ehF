a:6:{s:15:"storage_setting";O:18:"App\StorageSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:16:"storage_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:1;s:10:"filesystem";s:5:"local";s:9:"auth_keys";N;s:6:"status";s:7:"enabled";s:10:"created_at";s:19:"2025-02-17 10:13:00";s:10:"updated_at";s:19:"2025-02-17 10:13:00";}s:11:" * original";a:6:{s:2:"id";i:1;s:10:"filesystem";s:5:"local";s:9:"auth_keys";N;s:6:"status";s:7:"enabled";s:10:"created_at";s:19:"2025-02-17 10:13:00";s:10:"updated_at";s:19:"2025-02-17 10:13:00";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:3:{i:0;s:10:"filesystem";i:1;s:9:"auth_keys";i:2;s:6:"status";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}s:6:"_token";s:40:"HfucdlAaQyDXaMzn8YVFnbUlZ6lqq3QeK2S166Ms";s:9:"_previous";a:1:{s:3:"url";s:59:"http://recruit-seprocal.test/admin/settings/role-permission";}s:6:"_flash";a:2:{s:3:"old";a:0:{}s:3:"new";a:0:{}}s:3:"url";a:0:{}s:50:"login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d";i:1;}