<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="utf-8">
  <meta name="viewport"            content="width=device-width, initial-scale=1, shrink-to-fit=no">
  <meta name="description"         content="<?php echo e(!empty($metaDescription) ? $metaDescription : ''); ?>">

  <meta property="og:url"          content="<?php echo e(!empty($pageUrl) ? $pageUrl : ''); ?>" />
  <meta property="og:type"         content="website" />
  <meta property="og:title"        content="<?php echo e(!empty($metaTitle) ? $metaTitle : ''); ?>" />
  <meta property="og:description"  content="<?php echo e(!empty($metaDescription) ? $metaDescription : ''); ?>" />
  <meta property="og:image"        content="<?php echo e(!empty($metaImage) ? $metaImage : ''); ?>" />
  <meta property="og:image:width"  content="600" />
  <meta property="og:image:height" content="600" />

  <meta itemprop="name"            content="<?php echo e(!empty($metaTitle) ? $metaTitle : ''); ?>">
  <meta itemprop="description"     content="<?php echo e(!empty($metaDescription) ? $metaDescription : ''); ?>">
  <meta itemprop="image"           content="<?php echo e(!empty($metaImage) ? $metaImage : ''); ?>"> 

  <meta property="title"            content="<?php echo e(!empty($metaTitle) ? $metaTitle : ''); ?>">
  <meta property="description"     content="<?php echo e(!empty($metaDescription) ? $metaDescription : ''); ?>">

  <title><?php echo e($pageTitle); ?></title>

  <style>
    :root {
      --main-color: <?php echo e($frontTheme->primary_color); ?>;
    }

    <?php echo $frontTheme->front_custom_css; ?>

  </style>

  <!-- Styles -->
  <link href="<?php echo e(asset('froiden-helper/helper.css')); ?>" rel="stylesheet">
  <link href="<?php echo e(asset('assets/node_modules/toast-master/css/jquery.toast.css')); ?>" rel="stylesheet">

  <link href="<?php echo e(asset('front/assets/css/core.min.css')); ?>" rel="stylesheet">
  <link href="<?php echo e(asset('front/assets/css/select2.min.css')); ?>" rel="stylesheet">
  <link href="<?php echo e(asset('front/assets/css/thesaas.min.css')); ?>" rel="stylesheet">
  <link href="<?php echo e(asset('front/assets/css/style.css')); ?>" rel="stylesheet">
  <link href="<?php echo e(asset('front/assets/css/custom.css')); ?>" rel="stylesheet">
  <?php echo $__env->yieldPushContent('header-css'); ?>
  <link rel='stylesheet prefetch'
  href='//cdnjs.cloudflare.com/ajax/libs/flag-icon-css/0.8.2/css/flag-icon.min.css'>
  <link href="<?php echo e(asset('assets/node_modules/sweetalert/sweetalert.css')); ?>" rel="stylesheet">

  <!-- Favicons -->
  
  <link rel="icon" href="<?php echo e($companySetting->favicon_url); ?>" type="image/x-icon" />
  <link rel="manifest" href="<?php echo e(asset('favicon/manifest.json')); ?>">
  <meta name="msapplication-TileColor" content="#ffffff">
  <meta name="msapplication-TileImage" content="<?php echo e(asset('favicon/ms-icon-144x144.png')); ?>">
  <meta name="theme-color" content="#ffffff">
  <?php echo $__env->yieldPushContent('style'); ?>
  <style>
    .dropdown-toggle::after {
      right: 22px !important;
    }
  </style>
</head>

<body>

<!-- Topbar -->
<nav class="topbar topbar-inverse topbar-expand-md">
  <div class="container">

    <div class="topbar-left">
      <button class="topbar-toggler">&#9776;</button>
      <a class="topbar-brand" href="<?php echo e(url('/')); ?>">
        <img src="<?php echo e($global->logo_url); ?>" class="logo-inverse" alt="home" />
      </a>
    </div>
    
      

    <?php if($global->front_language == 1): ?>
    <div class="topbar-right d-none d-xl-block">
      <div class="d-inline-flex ml-200 language-drop">

        <div class="dropdown btn btn-default" style="margin-left: 390px;">
          <a href="#" class="dropdown-toggle text-capitalize" data-toggle="dropdown">
            <i class="flag-icon <?php if($language->language_code == 'en'): ?> flag-icon-us <?php else: ?>  flag-icon-<?php echo e($language->language_code); ?> <?php endif; ?>"></i> <?php echo e($language->language_name); ?>

          </a>
          <div class="dropdown-menu" style="margin-left: 10px">
            
            <?php $__empty_1 = true; $__currentLoopData = $languageSettings; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $language): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
              <a class="dropdown-item" data-lang-code="<?php echo e($language->language_code); ?>" href="javascript:;"> <span class="flag-icon <?php if($language->language_code == 'en'): ?> flag-icon-us <?php elseif($language->language_code == 'ar'): ?> flag-icon-ae <?php elseif($language->language_code == 'es-ar'): ?> flag-icon-ar <?php else: ?>   flag-icon-<?php echo e($language->language_code); ?> <?php endif; ?>"></span> <?php echo e($language->language_name); ?></a>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
            <?php endif; ?>
          </div>
        </div>
      </div>
    </div>
    <?php endif; ?>
 <?php if($global->job_alert_status == 1): ?>
  <?php if( isset($alertId) && !is_null($alertId)): ?>
    <div class="topbar-left ">
         
         <button class="btn btn-danger disable-job-alert" type="button"><?php echo app('translator')->get('modules.front.disableJobAlert'); ?></button>
         </div>
  <?php else: ?>
    <div class="topbar-left d-none d-xl-block">
         
         <button class="btn btn-primary" type="button" id="job-alert"><?php echo app('translator')->get('modules.front.jobAlert'); ?></button>
         </div>
  
  <?php endif; ?>
  <?php endif; ?>
  

  </div>
</nav>
<!-- END Topbar -->

<!-- Header -->
<header class="bg-img-shape">
    
  <div class="header inner-header" style="background-image: url(<?php echo e($frontTheme->background_image_url); ?>)" data-overlay="8">
    <div class="container text-center">

      <div class="row">
        <div class="col-12 col-lg-8 offset-lg-2">
          <?php echo $__env->yieldContent('header-text'); ?>
        </div>
      </div>

    </div>
  </div>
</header>
<!-- END Header -->

<!-- Main container -->
<main class="main-content">
  <?php echo $__env->yieldContent('content'); ?>
</main>
<div class="container">
  <br />
  <p>Seprocal es una empresa de servicios integrales para la minería, especializada en excavaciones subterráneas.</p>
  <p>Constituida en 2004, Seprocal desarrolló inicialmente proyectos de construcción de chimeneas con equipo trepador, actividad en la que la empresa fue pionera en el Perú, siendo actualmente indiscutible líder del mercado en esta actividad.</p>
  <p>Posteriormente, Seprocal incorporó diversos servicios complementarios, ejecutando labores horizontales desde el año 2010.</p>
  <p>En la actualidad, Seprocal brinda servicios especializados para diversos clientes mineros, generando casi un millar de empleos directos y operando un parque de más de 60 equipos.</p>
</div>

  <div class="modal fade bs-modal-md in" id="addJobAlert" role="dialog" aria-labelledby="myModalLabel"
    aria-hidden="true">
    <div class="modal-dialog modal-lg" id="modal-data-application">
      <div class="modal-content">
        <div class="modal-header">
          <h4 class="modal-title"><i class="icon-plus"></i> <?php echo app('translator')->get('app.department'); ?></h4>
          <button type="button" class="close" data-dismiss="modal" aria-hidden="true"><i
              class="fa fa-times"></i></button>
        </div>
        <div class="modal-body">
          Loading...
        </div>
        <div class="modal-footer">
          <button type="button" class="btn default" data-dismiss="modal">Close</button>
          <button type="button" class="btn blue">Save changes</button>
        </div>
      </div>
      <!-- /.modal-content -->
    </div>
    <!-- /.modal-dialog -->
  </div>
  
<!-- END Main container -->

<!-- Footer -->
<footer class="site-footer">
  <div class="container">
    <div class="row text-center">
      <div class="col-12 col-lg-12 mb-10">
         <?php $__empty_1 = true; $__currentLoopData = $customPages; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $customPage): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
          <a class="px-5 fw-400" href="<?php echo e(route('jobs.custom-page',$customPage->slug)); ?>"><span><?php echo e(($customPage->name)); ?></span></a>
         <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
         <?php endif; ?>

      </div>
      <div class="col-12 col-lg-12 fw-400">
        <p>&copy; <?php echo e(\Carbon\Carbon::today()->year); ?> <?php echo app('translator')->get('app.by'); ?> <?php echo e($companyName); ?></p>

      </div>
    </div>
  </div>
</footer>
<!-- END Footer -->



<!-- Scripts -->
<script src="<?php echo e(asset('front/assets/js/core.min.js')); ?>"></script>

<script src="<?php echo e(asset('front/assets/js/script_new.js')); ?>"></script>
<script src="<?php echo e(asset('front/assets/js/select2.min.js')); ?>"></script>
<script src="<?php echo e(asset('froiden-helper/helper.js')); ?>"></script>
<script src="<?php echo e(asset('assets/node_modules/toast-master/js/jquery.toast.js')); ?>"></script>
<script src="<?php echo e(asset('assets/node_modules/sweetalert/sweetalert.min.js')); ?>"></script>
<script>
  setActiveClassToLanguage();
  $('.language-drop .dropdown-item').click(function () {
    let code = $(this).data('lang-code');

    let url = '<?php echo e(route('jobs.changeLanguage', ':code')); ?>';
    url = url.replace(':code', code);

    if (!$(this).hasClass('active')) {
      $.easyAjax({
        url: url,
        type: 'POST',
        container: 'body',
        data: {
          _token: '<?php echo e(csrf_token()); ?>'
        },
        success: function (response) {
          if (response.status == 'success') {
            location.reload();
            setActiveClassToLanguage();
          }
        }
      })
    }
  })
  <?php if(isset($alertId) && !is_null($alertId)): ?>
  $('body').on('click', '.disable-job-alert', function(event){
     var id = <?php echo e($alertId); ?>;
       
     swal({
       title: "<?php echo app('translator')->get('errors.areYouSure'); ?>",
       text: "<?php echo app('translator')->get('errors.warnigJobAlert'); ?>",
       type: "warning",
       showCancelButton: true,
       confirmButtonColor: "#DD6B55",
       confirmButtonText: "<?php echo app('translator')->get('app.disable'); ?>",
       cancelButtonText: "<?php echo app('translator')->get('app.cancel'); ?>",
       closeOnConfirm: true,
       closeOnCancel: true
     }, function(isConfirm){
       if (isConfirm) {

         var url = "<?php echo e(route('jobs.disableJobAlert',':id')); ?>";
         url = url.replace(':id', id);

         var token = "<?php echo e(csrf_token()); ?>";

         $.easyAjax({
           type: 'POST',
           url: url,
           data: {'_token': token},
           success: function (response) {
             if (response.status == "success") {
               $.unblockUI();
               table._fnDraw();
             }
           }
         });
       }
     });
   });
<?php endif; ?>
  function setActiveClassToLanguage() {
    // language switcher
    if ('<?php echo e(\Cookie::has('language_code')); ?>') {
      $('.language-drop .dropdown-item').filter(function () {
        return $(this).data('lang-code') === '<?php echo e(\Cookie::get('language_code')); ?>'
      }).addClass('active');
    }
    else {
      $('.language-drop .dropdown-item').filter(function () {
        return $(this).data('lang-code') === '<?php echo e($global->locale); ?>'
      }).addClass('active');
    }
  }
  $(document).ready(function() {


  $('#job-alert').click(function() {
  var url = "<?php echo e(route('jobs.jobAlert')); ?>";
  console.log(url);
  $('.modal-title').html("<i class='icon-plus'></i> <?php echo app('translator')->get('modules.front.jobAlert'); ?>");
  $.ajaxModal('#addJobAlert', url);
  });

  

});

  
</script>
<?php echo $__env->yieldPushContent('footer-script'); ?>

</body>
</html><?php /**PATH E:\laragon\www\recruit-seprocal\resources\views/layouts/front.blade.php ENDPATH**/ ?>