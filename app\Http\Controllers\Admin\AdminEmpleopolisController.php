<?php

namespace App\Http\Controllers\Admin;

use Carbon\Carbon;
use App\Empleopolis;
use App\Helper\Files;
use App\JobApplication;
use App\ApplicationStatus;
use Illuminate\Http\Request;
use Froiden\Envato\Helpers\Reply;
use Illuminate\Support\Facades\DB;
use Yajra\DataTables\Facades\DataTables;
use App\Http\Requests\Empleopolis\StoreRequest;
use App\Http\Requests\Empleopolis\UpdateRequest;

class AdminEmpleopolisController extends AdminBaseController
{
    public function __construct()
    {
        parent::__construct();
        $this->pageTitle = "Empleopolis";
        $this->pageIcon = 'icon-doc';
    }

    public function index()
    {
        abort_if(!$this->user->cans('view_empleopolis'), 403);
        $this->candidates = JobApplication::all();
        return view('admin.empleopolis.index', $this->data);
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create(Request $request)
    {
        abort_if(!$this->user->cans('add_empleopolis'), 403);
        $this->candidates = JobApplication::all();
        $this->empleopolisDate = $request->date;
        $this->empleopolisStatus = '';
        $this->currentCandidateId = $request->id ?? null;
        return view('admin.empleopolis.create', $this->data)->render();
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(StoreRequest $request)
    {
        abort_if(!$this->user->cans('add_empleopolis'), 403);
        $jobApplication = JobApplication::find($request->candidate);
        $empleopolis = Empleopolis::updateOrCreate([
            'job_application_id'   => $request->candidate,
        ], [
            'filter_date'   => $request->empleopolisDate,
            'status'        => $request->empleopolisStatus,
            'comments'      => $request->comments,
        ]);
        if ($request->has('file')) {
            $empleopolis->results()->delete();
            $hashname = Files::uploadLocalOrS3($request->file, 'empleopolis/' . $jobApplication->id, null, null, false);
            $empleopolis->results()->create([
                'name' => 'Resultados',
                'hashname' => $hashname,
            ]);
        }
        $empleopolis->wasRecentlyCreated ? $message = __('messages.createdSuccessfully') : $message = __('messages.updatedSuccessfully');
        if ($empleopolis->status == 'suitable' && $jobApplication->status->status == 'empleopolis') {
            $jobApplication->status_id = ApplicationStatus::where('status', 'habilitación mina')->first()->id;
            $min_column_prority = JobApplication::where('status_id', ApplicationStatus::where('status', 'habilitación mina')->pluck('id'))->min('column_priority');
            $jobApplication->column_priority = $min_column_prority - 1;
            $jobApplication->save();
        } elseif ($jobApplication->status->status != 'empleopolis') {
            $jobApplication->status_id = ApplicationStatus::where('status', 'empleopolis')->first()->id;
            $min_column_prority = JobApplication::where('status_id', ApplicationStatus::where('status', 'empleopolis')->pluck('id'))->min('column_priority');
            $jobApplication->column_priority = $min_column_prority - 1;
            $jobApplication->save();
        }
        // return response()->json($empleopolis);
        return Reply::success(__('menu.empleopolis') . ' ' . $message, json_encode($empleopolis));

        // return Reply::redirect(route('admin.empleopolis.index'), __('menu.empleopolis') . ' ' . $message);
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        abort_if(!$this->user->cans('view_empleopolis'), 403);
        $this->empleopolis = Empleopolis::with(['jobApplication'])->find($id);
        return view('admin.empleopolis.show', $this->data)->render();
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        abort_if(!$this->user->cans('edit_empleopolis'), 403);
        $this->candidates = JobApplication::all();
        $this->empleopolis = Empleopolis::with(['jobApplication'])->find($id);
        return view('admin.empleopolis.edit', $this->data)->render();
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(UpdateRequest $request, $id)
    {
        abort_if(!$this->user->cans('add_empleopolis'), 403);
        $jobApplication = JobApplication::find($request->candidate);
        $empleopolis = Empleopolis::findOrFail($id);

        $empleopolis->job_application_id = $request->candidate;
        $empleopolis->filter_date = $request->empleopolisDate;
        $empleopolis->status = $request->empleopolisStatus;
        $empleopolis->comments = $request->comments;
        $empleopolis->save();
        if ($request->has('file')) {
            $empleopolis->results()->delete();
            $hashname = Files::uploadLocalOrS3($request->file, 'empleopolis/' . $jobApplication->id, null, null, false);
            $empleopolis->results()->create([
                'name' => 'Resultados',
                'hashname' => $hashname,
            ]);
        }
        if ($empleopolis->status == 'suitable' && $jobApplication->status->status == 'empleopolis') {
            $jobApplication->status_id = ApplicationStatus::where('status', 'habilitación mina')->first()->id;
            $min_column_prority = JobApplication::where('status_id', ApplicationStatus::where('status', 'habilitación mina')->pluck('id'))->min('column_priority');
            $jobApplication->column_priority = $min_column_prority - 1;
            $jobApplication->save();
        } elseif ($jobApplication->status->status != 'empleopolis') {
            $jobApplication->status_id = ApplicationStatus::where('status', 'empleopolis')->first()->id;
            $min_column_prority = JobApplication::where('status_id', ApplicationStatus::where('status', 'empleopolis')->pluck('id'))->min('column_priority');
            $jobApplication->column_priority = $min_column_prority - 1;
            $jobApplication->save();
        }

        return Reply::success(__('menu.empleopolis') . ' ' . __('messages.updatedSuccessfully'), json_encode($empleopolis));
        // return Reply::redirect(route('admin.empleopolis.index'), __('menu.empleopolis') . ' ' . __('messages.updatedSuccessfully'));
    }
    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        abort_if(!$this->user->cans('delete_empleopolis'), 403);

        Empleopolis::destroy($id);
        return Reply::success(__('messages.recordDeleted'));
    }

    /**
     * @param Request $request
     * @return mixed
     * @throws \Exception
     */
    public function data(Request $request)
    {
        abort_if(!$this->user->cans('view_empleopolis'), 403);

        $query = Empleopolis::selectRaw('empleopolis.id, job_applications.full_name, empleopolis.status, empleopolis.filter_date')
            ->leftjoin('job_applications', 'job_applications.id', 'empleopolis.job_application_id');

        // Filter by status
        if ($request->status != 'all' && $request->status != '') {
            $query = $query->where('empleopolis.status', $request->status);
        }

        // Filter By candidate
        if ($request->applicationID != 'all' && $request->applicationID != '') {
            $query = $query->where('job_applications.id', $request->applicationID);
        }

        // Filter by StartDate
        if ($request->startDate !== null && $request->startDate != 'null') {
            $query = $query->where(DB::raw('DATE(empleopolis.`filter_date`)'), '>=', "$request->startDate");
        }

        // Filter by EndDate
        if ($request->endDate !== null && $request->endDate != 'null') {
            $query = $query->where(DB::raw('DATE(empleopolis.`filter_date`)'), '<=', "$request->endDate");
        }

        return DataTables::of($query)
            ->addColumn('action', function ($row) {
                $action = '';
                if ($this->user->cans('view_empleopolis')) {
                    $action .= '<a href="#" data-row-id="' . $row->id . '" class="btn btn-info btn-circle view-data"
                      data-toggle="tooltip" onclick="this.blur()" data-original-title="' . __('app.view') . '"><i class="fa fa-search" aria-hidden="true"></i></a>';
                }
                if ($this->user->cans('edit_empleopolis')) {
                    $action .= '<a href="#" style="margin-left:4px" data-row-id="' . $row->id . '" class="btn btn-primary btn-circle edit-data"
                      data-toggle="tooltip" onclick="this.blur()" data-original-title="' . __('app.edit') . '"><i class="fa fa-pencil" aria-hidden="true"></i></a>';
                }

                if ($this->user->cans('delete_empleopolis')) {
                    $action .= ' <a href="#" class="btn btn-danger btn-circle sa-params"
                      data-toggle="tooltip" onclick="this.blur()" data-row-id="' . $row->id . '" data-original-title="' . __('app.delete') . '"><i class="fa fa-times" aria-hidden="true"></i></a>';
                }
                return $action;
            })
            ->addColumn('checkbox', function ($row) {
                return '
                    <div class="checkbox form-check">
                        <input id="' . $row->id . '" type="checkbox" name="id[]" class="form-check-input" value="' . $row->id . '" >
                        <label for="' . $row->id . '"></label>
                    </div>
                ';
            })
            ->editColumn('job_applications.full_name', function ($row) {
                return ucwords($row->full_name);
            })
            ->editColumn('filter_date', function ($row) {
                Carbon::setlocale(config('app.locale'));
                return is_null($row->filter_date) ? null : Carbon::parse($row->filter_date)->translatedFormat('d \de F \de Y');
            })
            ->editColumn('status', function ($row) {
                if ($row->status == 'pending') {
                    return '<label class="badge bg-warning">' . __('app.pending') . '</label>';
                }
                if ($row->status == 'suitable') {
                    return '<label class="badge bg-success">' . __('app.suitable') . '</label>';
                }
                if ($row->status == 'observed') {
                    return '<label class="badge bg-danger">' . __('app.observed') . '</label>';
                }
            })
            ->rawColumns(['action', 'status', 'job_applications.full_name', 'checkbox'])
            ->make(true);
    }

    public function dataByCandidate(Request $request, $id)
    {
        abort_if(!$this->user->cans('view_empleopolis'), 403);

        $query = Empleopolis::select('empleopolis.id', 'empleopolis.status', 'empleopolis.filter_date')
            ->leftjoin('job_applications', 'job_applications.id', 'empleopolis.job_application_id')
            ->where('empleopolis.job_application_id', $id);

        return DataTables::of($query)
            ->addColumn('action', function ($row) {
                $action = '';
                if ($this->user->cans('view_empleopolis')) {
                    $action .= '<a href="#" class="btn btn-info btn-circle view-data"
                    data-toggle="tooltip" onclick="showEmpleopolis(' . $row->id . ')" data-original-title="' . __('app.view') . '"><i class="fa fa-search" aria-hidden="true"></i></a>';
                }
                if ($this->user->cans('edit_empleopolis')) {
                    $action .= '<a href="#" style="margin-left:4px" class="btn btn-primary btn-circle edit-data"
                    data-toggle="tooltip" onclick="editEmpleopolis(' . $row->id . ')" data-original-title="' . __('app.edit') . '"><i class="fa fa-pencil" aria-hidden="true"></i></a>';
                }

                return $action;
            })
            ->editColumn('filter_date', function ($row) {
                Carbon::setlocale(config('app.locale'));

                return is_null($row->filter_date) ? null : Carbon::parse($row->filter_date)->translatedFormat('d \de F \de Y');
            })
            ->editColumn('status', function ($row) {
                if ($row->status == 'pending') {
                    return '<label class="badge bg-warning">' . __('app.pending') . '</label>';
                }
                if ($row->status == 'suitable') {
                    return '<label class="badge bg-success">' . __('app.suitable') . '</label>';
                }
                if ($row->status == 'observed') {
                    return '<label class="badge bg-danger">' . __('app.observed') . '</label>';
                }
            })
            ->rawColumns(['action', 'status'])
            ->make(true);
    }
}
