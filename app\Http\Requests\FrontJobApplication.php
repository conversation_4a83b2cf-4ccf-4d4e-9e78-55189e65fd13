<?php

namespace App\Http\Requests;

use App\Job;
use App\Question;
use App\JobApplication;
use Illuminate\Support\Arr;
use App\GoogleCaptchaSetting;
use App\Rules\CheckApplication;
use Illuminate\Validation\Rule;
use Illuminate\Foundation\Http\FormRequest;

class FrontJobApplication extends CoreRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        $google_captcha = GoogleCaptchaSetting::first();
        $job = Job::select('id', 'required_columns', 'section_visibility')->where('id', $this->job_id)->first();
        $applicationMail = JobApplication::with('status')->where('email', request()->email)->where('job_id', $this->job_id)->first();
        if (!is_null($applicationMail) && $applicationMail->status->status != 'rejected') {
            $rules = [
                'first_name' => 'required',
                'last_name' => 'required',
                'email' => [
                    'required', 'email', new CheckApplication(),
                    // Rule::unique('job_applications')->where(function ($query) {
                    //     return $query->where('job_id', $this->job_id);
                    // })
                ],
                'phone' => 'required|numeric',
                'dni' => 'required|numeric',
                'departamento_id' => 'required|integer|exists:departamentos,id',
                'provincia_id' => 'required|integer|exists:provincias,id',
                'email' => 'required|email',
                'phone' => 'required|numeric',
            ];
        } else {
            $rules = [
                'first_name' => 'required',
                'last_name' => 'required',
                'email' => 'required|email',
                'phone' => 'required|numeric',
                'dni' => 'required|numeric',
                'departamento_id' => 'required|integer|exists:departamentos,id',
                'provincia_id' => 'required|integer|exists:provincias,id',
                'phone' => 'required|numeric',
            ];
        }

        if ($google_captcha->status == 'active' && $google_captcha->job_apply_page == 'active') {
            $rules['recaptcha'] = 'required';
        }

        // if ($sectionVisibility) {
        //     foreach ($sectionVisibility as $key => $section) {
        //         if ($section === 'yes') {
        //             if ($key === 'profile_image') {
        //                 $rules = Arr::add($rules, 'photo', 'required|mimes:jpeg,jpg,png');
        //             }
        //             if ($key === 'resume') {
        //                 $rules = Arr::add($rules, 'resume', 'required');
        //             }
        //             if ($key === 'terms_and_conditions') {
        //                 $rules = Arr::add($rules, 'term_agreement', 'required');
        //             }
        //         }
        //     }
        // }

        $rules = Arr::add($rules, 'gender', 'required|in:male,female,others');
        $rules = Arr::add($rules, 'dob', 'required|date');
        // if ($requiredColumns['country']) {
        //     $rules = Arr::add($rules, 'country', 'required|integer|min:1');
        //     $rules = Arr::add($rules, 'state', 'required|integer|min:1');
        //     $rules = Arr::add($rules, 'city', 'required');
        // }

        $this->get('answer');
        if (!empty($this->get('answer'))) {
            foreach ($this->get('answer') as $key => $value) {

                $answer = Question::where('id', $key)->first();
                if ($answer->required == 'yes') {
                    $rules["answer.{$key}"] = 'required';
                }
            }
        }

        return $rules;
    }

    public function messages()
    {
        return [
            'answer.*.required' => 'This answer field is required.',
            'dob.required' => 'La fecha de nacimiento es obligatoria.',
            'departamento_id.required' => 'Por favor seleccione el departamento.',
            'provincia_id.required' => 'Por favor seleccione la provinca.',
            'email.unique' => 'Ya has presentado una solicitud a este trabajo con este correo electrónico.',
        ];
    }
}
