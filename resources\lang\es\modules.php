<?php

return  [
    'accountSettings' => [
        'companyName' => 'Nombre de la Empresa',
        'updateTitle' => 'Actualizar Configuración de la Organización',
        'companyEmail' => 'Correo Electrónico de la Empresa',
        'companyPhone' => 'Teléfono de la Empresa',
        'companyWebsite' => 'Sitio Web de la Empresa',
        'companyLogo' => 'Logo de la Empresa',
        'companyFavIcon' => 'FavIcon de la Empresa',
        'companyAddress' => 'Dirección de la Empresa',
        'defaultTimezone' => 'Zona Horaria Predeterminada',
        'defaultCurrency' => 'Moneda Predeterminada',
        'changeLanguage' => 'Cambiar Idioma',
        'currencySetting' => 'Cambiar Moneda',
        'uploadLogo' => 'Sube tu logo',
        'getLocation' => 'Establecer ubicación actual',
        'updateEnableDisable' => 'Actualización de la Aplicación',
        'updateEnableDisableTest' => 'Habilitar/Deshabilitar configuración de actualización de la aplicación.',
        'languageEnable' => 'Habilitar/Deshabilitar idioma desde el frontend',
        'frontLanguage' => 'Idioma del frontend',
        'jobAlert' => 'Alerta de Trabajo',
        'jobAlertEnable' => 'Habilitar/Deshabilitar Alerta de Trabajo desde el frontend',
    ],
    'roles' => [
        'addRole' => 'Gestionar Rol',
    ],
    'permission' => [
        'projectNote' => 'El usuario puede ver los detalles básicos de los proyectos asignados a él incluso sin ningún permiso.',
        'attendanceNote' => 'El usuario puede ver su propia asistencia incluso sin ningún permiso.',
        'taskNote' => 'El usuario puede ver las tareas asignadas a él incluso sin ningún permiso.',
        'ticketNote' => 'El usuario puede ver los tickets generados por él como predeterminado incluso sin ningún permiso.',
        'eventNote' => 'El usuario puede ver los eventos a los que debe asistir como predeterminado incluso sin ningún permiso.',
        'selectAll' => 'Seleccionar Todo',
        'addRoleMember' => 'Gestionar Miembros del Rol',
        'addMembers' => 'Agregar Miembros',
        'roleName' => 'Nombre del Rol',
    ],
    'jobs' => [
        'jobName' => 'Puesto de trabajo',
        'jobTitle' => 'Título del requerimiento',
        'jobCode' => 'Código',
        'jobDescription' => 'Funciones',
        'jobRequirement' => 'Requisitos',
        'totalPositions' => 'Total de Vacantes',
        'metaTitle' => 'Meta Título',
        'metaDescription' => 'Meta Descripción',
        'sectionVisibility' => 'Visibilidad de la Sección',
        'profileImage' => 'Imagen de Perfil',
        'jobType' => 'Tipo de Requerimiento',
        'workExperience' => 'Experiencia Laboral',
        'showPayBy' => 'Mostrar Pago Por',
        'range' => 'Rango',
        'startingSalary' => 'Salario Inicial',
        'maximumSalary' => 'Salario Máximo',
        'exactSalary' => 'Salario Exacto',
        'rate' => 'Tipo de pago',
        'hour' => 'Hora',
        'day' => 'Día',
        'week' => 'Semana',
        'month' => 'Mes',
        'year' => 'Año',
        'resume' => 'Currículum',
        'coverLetter' => 'Carta de Presentación',
        'termsAndConditions' => 'Términos y Condiciones',
        'showJobType' => 'Mostrar Tipo de Trabajo en el frontend',
        'showWorkExperience' => 'Mostrar Experiencia Laboral en el frontend',
        'showSalary' => 'Mostrar Salario en el frontend',
        'jobapplicationlimitation' => 'Limitación de Solicitud de Trabajo (En días)',
        'gogglemapapikey' => 'Clave API de Google Map',
        'scantopay' => 'Escanear para Aplicar',
        'canceledReason' => 'Motivo de Cancelación',
    ],
    'front' => [
        'jobOpenings' => 'Ofertas de Trabajo',
        'visitMainWebsite' => 'Visitar el Sitio Web Principal',
        'homeHeader' => 'Seprocal - Ofertas de Trabajo',
        'applyForJob' => 'Aplicar para este Trabajo',
        'shareJob' => 'Compartir esta oferta de trabajo',
        'applicationForm' => 'Formulario de Solicitud',
        'personalInformation' => 'Información Personal',
        'fullName' => 'Tu Nombre Completo (Requerido)',
        'firstName' => 'Nombres (Requerido)',
        'lastName' => 'Apellidos (Requierido)',
        'email' => 'Tu dirección de correo electrónico (Requerido)',
        'phone' => 'Tu número de teléfono (Requerido)',
        'photoFileType' => 'Aceptamos archivos PNG, JPG y JPEG',
        'photo' => 'Foto',
        'resume' => 'CV o Currículum',
        'certificado' => 'Certificado Único Laboral (CertiJoven / CertiAdulto)',
        'coverLetter' => 'Carta de Presentación',
        'submitApplication' => 'Enviar Solicitud',
        'applySuccessMsg' => 'Has aplicado exitosamente para esta posición. Nos pondremos en contacto contigo muy pronto :)',
        'jobOpeningHeading' => '¡Trabajando en Seprocal!',
        'jobOpeningText' => 'Queremos que las personas prosperen en Seprocal; <br>creemos que haces tu mejor trabajo cuando te sientes mejor.',
        'questions' => 'Preguntas',
        'additionalDetails' => 'Detalles Adicionales',
        'resumeFileType' => 'Aceptamos archivos JPEG, JPG, PNG, DOC, DOCX, RTF, XLS, XLSX y PDF',
        'imageFileType' => 'Aceptamos archivos JPEG, JPG, PNG y WEBP',
        'linkedinSignin' => 'Iniciar sesión con LinkedIn',
        'gender' => 'Género',
        'dob' => 'Fecha de Nacimiento (Requerido)',
        'country' => 'País',
        'male' => 'Masculino',
        'female' => 'Femenino',
        'others' => 'Otros',
        'selectCountry' => 'Seleccionar País',
        'selectState' => 'Seleccionar Estado',
        'selectCity' => 'Ingresar Nombre de la Ciudad',
        'zipCode' => 'Código Postal',
        'yourAnswer' => 'Tu Respuesta',
        'legalTerm' => 'Términos y Condiciones',
        'agreeWithTerm' => 'Estoy de acuerdo con los Términos y Condiciones anteriores',
        'agreeWithTruth' => 'Declaro la veracidad de los datos consignados en este formulario y entiendo que cualquier falsedad o inexactitud puede resultar en la anulación de mi solicitud.',
        'noData' => 'No se encontraron más registros',
        'loadMore' => 'Cargar Más',
        'allCategory' => 'Todas las Categorías',
        'allLocation' => 'Todas las Ubicaciones',
        'jobAlert' => 'Crear Alerta de Trabajo',
        'disableJobAlert' => 'Desactivar alerta de trabajo',
        'keywords' => 'Palabras Clave',
        'allSkill' => 'Todas las Habilidades',
        'allCompany' => 'Todas las Empresas',
    ],
    'jobApplication' => [
        'applicantName' => 'Nombre del Solicitante',
        'resume' => 'Currículum',
        'appliedAt' => 'Aplicado en',
        'appliedFor' => 'Aplicado Para',
        'status' => 'Estado',
        'job' => 'Trabajo',
        'location' => 'Ubicación',
        'startDate' => 'Fecha de Inicio',
        'sendDate' => 'Fecha de Envío',
        'pending' => 'Pendiente',
        'approved' => 'Aprobado',
        'all' => 'Todos',
        'allStatus' => 'Todos los Estados',
        'allLocation' => 'Todas las Ubicaciones',
        'allJobs' => 'Todos los Trabajos',
        'allCompany' => 'Todas las Empresas',
        'selectJob' => 'Seleccionar Trabajo',
        'applicantNotes' => 'Notas del Solicitante',
        'addNote' => 'Agregar Nota',
        'boardView' => 'Vista de Tablero',
        'archiveApplication' => 'Archivar Solicitud',
        'deleteApplication' => 'Eliminar Solicitud',
        'rejectApplication' => 'Rechazar Solicitud',
        'addSkills' => 'Agregar Habilidades',
        'skills' => 'Habilidades',
        'updateSkills' => 'Actualizar Habilidades',
        'enterName' => 'Buscar',
        'unarchiveApplication' => 'Desarchivar Solicitud',
        'documents' => 'Documentos',
        'allQuestion' => 'Todas las Preguntas Personalizadas',
        'viewDocuments' => 'Ver Documentos',
        'document' => [
            'name' => 'Nombre',
            'file' => 'Archivo',
            'fileNote' => 'Aceptamos archivos JPEG, JPG, PNG, GIF, TXT, DOC, DOCX, RTF, XLS, XLSX y PDF',
        ],
        'newStatus' => 'Nuevo Estado',
        'createStatus' => 'Crear Estado',
        'editStatus' => 'Editar Estado',
        'statusName' => 'Nombre',
        'statusColor' => 'Color',
        'statusPosition' => 'Posición',
        'noChange' => 'Sin Cambio',
    ],
    'themeSettings' => [
        'themePrimaryColor' => 'Color Primario del Tema',
        'adminPanelCustomCss' => 'CSS personalizado del panel de administración',
        'frontSiteCustomCss' => 'CSS personalizado del sitio frontal',
    ],
    'dashboard' => [
        'totalOpenings' => 'Total de Vacantes',
        'totalApplications' => 'Total de Solicitudes',
        'totalHired' => 'Total Contratados',
        'totalRejected' => 'Total Rechazados',
        'newApplications' => 'Nuevas Solicitudes',
        'shortlistedCandidates' => 'Candidatos Preseleccionados',
        'todayInterview' => 'Entrevistas de Hoy',
        'totalCompanies' => 'Total de Empresas',
        'activeCompanies' => 'Empresas Activas',
        'inactiveCompanies' => 'Empresas Inactivas',
        'totalJobs' => 'Total de Requerimientos',
        'activeJobs' => 'Requerimientos Activos',
        'inactiveJobs' => 'Requerimientos Inactivos',
        'totalJobsAlert' => 'Total de Alertas de Requerimiento',
        'activeJobsAlert' => 'Alertas de Requerimiento Activas',
        'inactiveJobsAlert' => 'Alertas de Requerimiento Inactivas',
    ],
    'update' => [
        'systemDetails' => 'Detalles del Sistema',
        'updateTitle' => 'Actualizar a la Nueva Versión',
        'updateDatabase' => 'Actualizar Base de Datos',
        'fileReplaceAlert' => 'Para actualizar la aplicación a la nueva versión, consulte la documentación para obtener instrucciones.',
        'updateDatabaseButton' => 'Haga clic para actualizar la base de datos',
        'newUpdate' => 'Nueva actualización disponible',
        'updateNow' => 'Actualizar Ahora',
        'updateAlternate' => 'Si el botón Actualizar Ahora no funciona, siga las instrucciones de actualización mencionadas en la documentación.',
    ],
    'interviewSchedule' => [
        'noSchedule' => 'No hay entrevistas programadas para este candidato.',
        'chooseEmployee' => 'Elegir Empleado',
        'chooseCandidate' => 'Elegir Candidato',
        'scheduleDate' => 'Fecha de Programación',
        'candidate' => 'Candidato',
        'employee' => 'Empleado',
        'addSh' => 'Empleado',
        'interviewSchedule' => 'Programación de Entrevistas',
        'addInterviewSchedule' => 'Agregar Programación de Entrevista',
        'appliedFor' => 'Aplicado Para',
        'status' => 'Estado',
        'job' => 'Requerimiento',
        'assignedEmployee' => 'Empleado Asignado',
        'employeeResponse' => 'Respuesta del Empleado',
        'calendar' => 'Calendario',
        'scheduleTime' => 'Hora de Programación',
        'scheduleDetail' => 'Detalle de la Programación de la Entrevista',
        'assignedEmployeeForInterview' => 'Empleado Asignado para la Entrevista',
        'candidateDetail' => 'Detalle del Candidato',
        'scheduleEditDetail' => 'Detalle de la Programación',
        'employeeCurrentResponse' => 'Respuesta Actual del Empleado',
        'noEmployeeAssigned' => 'No hay empleado asignado.',
        'scheduleInterview' => 'Programar Entrevista',
        'resume' => 'Currículum',
        'addComment' => 'Agregar un Comentario',
        'comment' => 'Comentario',
        'comments' => 'Comentarios',
        'commentsBy' => 'Comentario Por',
        'calendarView' => 'Vista de Calendario',
        'allCandidates' => 'Todos los Candidatos',
        'allStatus' => 'Todos los Estados',
        'selectStatus' => 'Cambiar Estado',
        'interviewTitle' => 'Título de la Entrevista',
        'host' => 'Anfitrión',
        'interviewType' => 'Tipo de Entrevista',
        'endDate' => 'Fecha de Finalización',
        'endTime' => 'Hora de Finalización',
    ],
    'zoommeeting' => [
        'addMeeting' => 'Agregar Reunión',
        'tableView' => 'Vista de Tabla',
        'meetingName' => 'Título de la Reunión',
        'where' => 'Dónde',
        'description' => 'Descripción (opcional)',
        'startOn' => 'Comienza En',
        'endOn' => 'Termina En',
        'addAttendees' => 'Agregar Asistentes',
        'repeat' => 'Repetir',
        'repeatEvery' => 'Repetir cada',
        'cycles' => 'Ciclos',
        'cyclesToolTip' => 'La recurrencia se detendrá después del número de ciclos. Déjelo en blanco para infinito.',
        'viewAttendees' => 'Ver Asistentes',
        'remindBefore' => 'Recordar antes',
        'reminder' => 'Enviar Recordatorio',
        'hostVideoStatus' => 'Estado del Video del Anfitrión',
        'participantVideoStatus' => 'Estado del Video del Participante',
        'editMeeting' => 'Editar Reunión',
        'meetingDetails' => 'Detalles de la Reunión',
        'calendarView' => 'Vista de Calendario',
        'startUrl' => 'Iniciar Reunión',
        'endUrl' => 'Terminar Reunión',
        'joinUrl' => 'Unirse a la Reunión',
        'eventName' => 'Nombre del Evento',
        'waiting' => 'Esperando',
        'live' => 'En Vivo',
        'endMeeting' => 'Terminar Reunión',
        'attendeeValidation' => 'Requerido si no se selecciona ni empleado ni cliente.',
        'meetingPassword' => 'Contraseña de la Reunión',
        'cancelMeeting' => 'Cancelar Reunión',
        'occurrence' => 'Ocurrencia',
        'hideFinishedMeetings' => 'Ocultar Finalizadas',
        'deleteAllOccurrences' => 'Eliminar Todas las Ocurrencias',
        'meetingHost' => 'Anfitrión de la Reunión',
        'categoryName' => 'Nombre de la Categoría',
        'startMeeting' => 'Iniciar Reunión',
        'day' => 'Día',
        'week' => 'Semana',
        'month' => 'Mes',
        'hour' => 'Hora',
        'minute' => 'Minuto',
        'meetingStatus' => 'Estado de la Reunión',
        'startTime' => 'Hora de Inicio',
        'endTime' => 'Hora de Finalización',
    ],
    'configuracionZoom' => [
        'configuracion' => 'Actualizar Configuraciones',
        'zoomapikey' => 'Clave Api de Zoom',
        'zoomapisecret' => 'Secreto de Zoom',
        'habilitarConfiguracion' => 'Habilitar Configuración de Zoom',
        'abrirZoomApp' => '¿Abrir en la Aplicación Cliente de Zoom?',
        'informacionWebhook' => 'Agrega esto como tu URL de webhook en la Aplicación Zoom',
        'tokenSecreto' => 'Token Secreto',
        'idCuenta' => 'Id de Cuenta',
        'meetingsdkapikey' => 'Clave Api del SDK de Reuniones',
        'meetingSdkApiSecret' => 'Secreto del SDK de Reuniones',
    ],
    'meetings' => [
        'todosEmpleados' => 'Todos los Empleados',
        'todosClientes' => 'Todos los Clientes',
        'agregarEmpleados' => 'Agregar Empleados',
        'agregarClientes' => 'Agregar Clientes',
        'idReunion' => 'Id de la Reunión',
        'nombreReunion' => 'Título de la Reunión',
        'inicioEn' => 'Inicio En',
        'finEn' => 'Fin En',
        'estado' => 'Estado',
        'accion' => 'Acción',
        'deshabilitado' => 'Deshabilitado',
        'habilitado' => 'Habilitado',
        'enLinea' => 'En Línea',
        'fueraDeLinea' => 'Fuera de Línea',
    ],
    'projects' => [
        'name' => 'Nombre del proyecto',
        'description' => 'Descripción del proyecto',
    ],
    'cost_centers' => [
        'name' => 'Nombre del centro de costo',
        'description' => 'Descripción del centro de costo',
    ],
    'message' => [
        'elegirMiembro' => 'Elegir Miembro',
        'seleccionarCliente' => 'Seleccionar Cliente',
        'sinCategoriaAgregada' => 'No se ha agregado ninguna categoría',
        'porFavorSeleccionarCategoria' => 'Por favor seleccione una categoría',
        'categoriaAgregada' => 'Categoría agregada exitosamente',
    ],
    'company' => [
        'showFrontend' => '¿Mostrar el nombre de la empresa en el Frontend?',
        'registradoEn' => 'Registrado en',
    ],
    'message' => [
        'chooseMember' => 'Choose Member',
        'selectClient' => 'Select Client',
        'noCategoryAdded' => 'No Category Added',
        'pleaseSelectCategory' => 'Please Select Category',
        'categoryAdded' => 'Category Added successfully',
    ],
    'company' => [
        'showFrontend' => '¿Mostrar el nombre de la empresa en el Frontend?',
        'registeredOn' => 'Registrado en',
    ],
    'sticky' => [
        'addNote' => 'Agregar Nota',
        'lastUpdated' => 'Actualizado',
        'colors' => 'Colores',
    ],
    'newJobEmail' => [
        'mailStatus' => 'Estado del Correo',
        'mailSent' => 'Enviado',
        'mailNotSent' => 'No Enviado',
        'applicantsSelected' => 'Todos los <strong>:applicantsOnPage</strong> solicitantes en esta página están seleccionados.',
        'allApplicantsSelected' => 'Todos los <strong>:totalApplicants</strong> solicitantes están seleccionados.',
        'selectAllApplicants' => 'Seleccionar todos los :totalApplicants solicitantes',
        'clearSelection' => 'Borrar selección',
        'selectMailStatus' => 'Seleccionar estado del correo',
        'selectJob' => 'Seleccionar Trabajo',
    ],
    'linkedInSettings' => [
        'status' => 'Estado',
        'client_id' => 'Id del Cliente',
        'client_secret' => 'Secreto del Cliente',
        'callback_url' => 'URL de Retorno',
        'activateLinkedinSignin' => 'Permitir Inicio de Sesión a través de LinkedIn',
        'sslError' => '* Su sitio no tiene Certificado SSL (https). Por favor, instale el Certificado SSL para poder usar el Inicio de Sesión de LinkedIn.',
    ],
    'module' => [
        'todos' => [
            'todoList' => 'Lista de Tareas',
            'viewAll' => 'Ver Todo',
            'pendingTasks' => 'Tareas Pendientes',
            'completedTasks' => 'Tareas Completadas',
            'noPendingTasks' => '¡No hay tareas pendientes disponibles!',
            'noCompletedTasks' => 'No hay tareas marcadas como completadas.',
            'createTodoItem' => 'Crear Elemento de Tarea',
            'editTodoItem' => 'Editar Elemento de Tarea',
            'form' => [
                'title' => 'Título',
                'status' => 'Estado',
            ],
            'completed' => 'Completado',
            'pending' => 'Pendiente',
        ],
    ],
    'datatables' => [
        'emptyTable' => 'No hay datos disponibles en la tabla',
        'info' => 'Mostrando _START_ a _END_ de _TOTAL_ entradas',
        'infoEmpty' => 'Mostrando 0 a 0 de 0 entradas',
        'infoFiltered' => '(filtrado de _MAX_ entradas totales)',
        'infoPostFix' => '',
        'lengthMenu' => 'Mostrar _MENU_ entradas',
        'loadingRecords' => 'Cargando...',
        'zeroRecords' => 'No se encontraron registros coincidentes',
        'paginate' => [
            'first' => 'Primero',
            'last' => 'Último',
            'next' => 'Siguiente',
            'previous' => 'Anterior',
        ],
        'aria' => [
            'sortAscending' => ': activar para ordenar la columna ascendente',
            'sortDescending' => ': activar para ordenar la columna descendente',
        ],
        'processing' => 'Procesando...',
        'search' => 'Buscar:',
        'infoThousands' => ',',
    ],
    'languageSettings' => [
        'defaultLanguageCannotBeModified' => 'El idioma predeterminado no se puede actualizar ni eliminar.',
        'statusDisabledNote' => 'No se puede cambiar el estado del Idioma Activo',
    ],
    'jobApplicationStatus' => [
        'applied' => 'Aplicado',
        'phoneScreen' => 'Entrevista Telefónica',
        'interview' => 'Entrevista',
        'hired' => 'Contratado',
        'rejected' => 'Rechazado',
    ],
    'applicationArchive' => [
        'enterSkill' => 'Ingrese la habilidad para filtrar, ej: php, java, etc.',
        'exportFileDescription' => 'Archivo de Base de Datos de Candidatos',
        'exportFileName' => 'base-de-datos-de-candidatos',
    ],
    'applicationSetting' => [
        'showProfileImage' => '¿Mostrar Imagen de Perfil en la Página de Aplicación?',
        'legalTermText' => 'Texto de Términos Legales',
        'formSettings' => 'Configuraciones del Formulario',
        'mailSettings' => 'Configuraciones de Correo',
        'googlemapapiSetting' => 'Configuraciones de la API de Google Map',
    ],
    'report' => [
        'jobapplication' => 'Solicitud de Trabajo',
        'job' => 'Trabajo Publicado',
        'candidatehired' => 'Candidato Contratado',
        'interviewschedule' => 'Programación de Entrevistas',
        'jobapplicationstatus' => 'Estado de la Solicitud de Trabajo',
    ],
    'currency' => [
        'currencyName' => 'Nombre de la Moneda',
        'currencySymbol' => 'Símbolo de la Moneda',
        'currencyStatus' => 'Estado de la Moneda',
        'currencyCode' => 'Código de la Moneda',
    ],
    'permissions' => [
        'job categories' => 'Categorías de Trabajo',
        'job skills' => 'Habilidades',
        'job applications' => 'Solicitudes de Trabajo',
        'job locations' => 'Ubicaciones de Trabajo',
        'jobs' => 'Requerimientos',
        'settings' => 'Configuracion',
        'team' => 'Usuarios',
        'question' => 'Preguntas',
        'schedule' => 'Entrevistas',
        'company' => 'Empresas',
        'cost center' => 'Centros de Costo',
        'empleopolis' => 'Empleopolis',
        'adjuncts' => 'Anexo 04',
        'signus' => 'Signus',
        'client filters' => 'Filtros de Cliente',
        'internal filter' => 'Filtro Interno',
        'medical exams' => 'Exámenes Médicos',
        'job descriptions' => 'Puestos de Trabajo',
        'reports' => 'Reportes',
    ]
];
