<?php

namespace App\Http\Controllers\Admin;

use App\Helper\Reply;
use App\JobDescription;
use Illuminate\Support\Str;
use Illuminate\Http\Request;
use Yajra\DataTables\DataTables;
use App\Http\Requests\Admin\JobDescription\StoreRequest;

class AdminJobDescriptionController extends AdminBaseController
{
    public function __construct()
    {
        parent::__construct();
        $this->pageTitle = __('app.job_description');
        $this->pageIcon = 'icon-user';
    }

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        abort_if(!$this->user->cans('view_job_description'), 403);

        return view('admin.job_descriptions.index', $this->data);
    }

    public function create()
    {
        abort_if(!$this->user->cans('add_job_description'), 403);

        return view('admin.job_descriptions.create', $this->data);
    }


    public function edit($id)
    {
        abort_if(!$this->user->cans('edit_job_description'), 403);

        $this->job_description = JobDescription::find($id);
        return view('admin.job_descriptions.edit', $this->data);
    }

    public function store(StoreRequest $request)
    {
        abort_if(!$this->user->cans('add_job_description'), 403);

        $data = $request->all();
        JobDescription::create($data);
        return Reply::redirect(route('admin.job_descriptions.index'), __('menu.job_description') . ' ' . __('messages.createdSuccessfully'));
    }

    public function update(StoreRequest $request, $id)
    {
        abort_if(!$this->user->cans('edit_job_description'), 403);

        $data = $request->all();
        $jobDescription = JobDescription::findOrFail($id);
        $jobDescription->update($data);

        $jobDescriptionData = JobDescription::all();
        return Reply::redirect(route('admin.job_descriptions.index'), __('menu.job_description') . ' ' . __('messages.job_descriptionUpdated'));
        // return Reply::successWithData(__('messages.job_descriptionUpdated'),['data' => $jobDescriptionData]);
    }

    public function destroy($id)
    {
        abort_if(!$this->user->cans('delete_job_description'), 403);

        JobDescription::destroy($id);

        $jobDescriptionData = JobDescription::all();
        return Reply::successWithData(__('messages.recordDeleted'), ['data' => $jobDescriptionData]);
    }

    public function show($id)
    {
        //
    }

    public function data()
    {
        abort_if(!$this->user->cans('view_job_description'), 403);

        $jobDescriptions = JobDescription::all();

        return DataTables::of($jobDescriptions)
            ->addColumn('action', function ($row) {
                $action = '';

                if ($this->user->cans('edit_job_description')) {
                    $action .= '<a href="' . route('admin.job_descriptions.edit', [$row->id]) . '" class="btn btn-primary btn-circle"
                      data-toggle="tooltip" onclick="this.blur()" data-original-title="'.__('app.edit').'"><i class="fa fa-pencil" aria-hidden="true"></i></a>';
                }

                if ($this->user->cans('delete_job_description')) {
                    $action .= ' <a href="#" class="btn btn-danger btn-circle sa-params"
                      data-toggle="tooltip" onclick="this.blur()" data-row-id="' . $row->id . '" data-original-title="'.__('app.delete').'"><i class="fa fa-times" aria-hidden="true"></i></a>';
                }
                return $action;
            })
            ->editColumn('name', function ($row) {
                return ucfirst($row->name);
            })
            ->editColumn('description', function ($row) {
                return Str::of(strip_tags($row->description))
                    ->words(40, '...');
            })
            ->editColumn('requirements', function ($row) {
                return Str::of(strip_tags($row->requirements))
                    ->words(40, '...');
            })
            ->addIndexColumn()
            ->make(true);
    }

}
