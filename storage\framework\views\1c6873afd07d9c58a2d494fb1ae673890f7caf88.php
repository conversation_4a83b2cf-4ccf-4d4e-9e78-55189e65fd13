<!DOCTYPE html>
<html>

<head>
  <meta charset="utf-8">
  <meta content="IE=edge" http-equiv="X-UA-Compatible">
  <!-- Favicon icon -->
  <link href="<?php echo e($companySetting->favicon_url); ?>" rel="icon" type="image/x-icon" />
  <link href="<?php echo e(asset('favicon/manifest.json')); ?>" rel="manifest">
  <meta content="#ffffff" name="msapplication-TileColor">
  <meta content="<?php echo e(asset('favicon/ms-icon-144x144.png')); ?>" name="msapplication-TileImage">
  <meta content="#ffffff" name="theme-color">

  <title><?php echo app('translator')->get('app.adminPanel'); ?> | <?php echo e($pageTitle); ?></title>
  <!-- Tell the browser to be responsive to screen width -->
  <meta content="width=device-width, initial-scale=1" name="viewport">

  <!-- Font Awesome -->
  <link href="https://maxcdn.bootstrapcdn.com/font-awesome/4.4.0/css/font-awesome.min.css" rel="stylesheet">

  <!-- Simple line icons -->
  <link href="https://cdnjs.cloudflare.com/ajax/libs/simple-line-icons/2.4.1/css/simple-line-icons.css" rel="stylesheet">

  <!-- Themify icons -->
  <link href="<?php echo e(asset('assets/icons/themify-icons/themify-icons.css')); ?>" rel="stylesheet">
  <!-- Ionicons -->
  <link href="https://code.ionicframework.com/ionicons/2.0.1/css/ionicons.min.css" rel="stylesheet">
  <!-- Theme style -->

  <link href="<?php echo e(asset('froiden-helper/helper.css')); ?>" rel="stylesheet">
  <link href="<?php echo e(asset('assets/node_modules/toast-master/css/jquery.toast.css')); ?>" rel="stylesheet">
  <link href="<?php echo e(asset('assets/node_modules/sweetalert/sweetalert.css')); ?>" rel="stylesheet">
  <link href='https://cdn.jsdelivr.net/npm/bootstrap-select@1.13.0/dist/css/bootstrap-select.min.css' rel='stylesheet prefetch'>

  <link href="<?php echo e(asset('assets/plugins/datatables/dataTables.bootstrap4.css')); ?>" rel="stylesheet">
  <link href="<?php echo e(asset('assets/node_modules/select2/dist/css/select2.min.css')); ?>" rel="stylesheet" type="text/css" />
  <link href="<?php echo e(asset('assets/node_modules/bootstrap-select/bootstrap-select.min.css')); ?>" rel="stylesheet">
  <link href="<?php echo e(asset('assets/dist/css/adminlte.min.css')); ?>" rel="stylesheet">
  <link href="<?php echo e(asset('assets/node_modules/Magnific-Popup-master/dist/magnific-popup.css')); ?>" rel="stylesheet">

  <?php echo $__env->yieldPushContent('head-script'); ?>

  <link href='//cdnjs.cloudflare.com/ajax/libs/flag-icon-css/0.8.2/css/flag-icon.min.css' rel='stylesheet prefetch'>

  <link href="<?php echo e(asset('css/custom.css')); ?>" rel="stylesheet">
  <style>
    :root {
      --main-color: <?php echo e($adminTheme->primary_color); ?>;
    }

    .well,
    pre {
      background: #fff;
      border-radius: 0;
    }

    .btn-group-xs>.btn,
    .btn-xs {
      padding: .25rem .4rem;
      font-size: .875rem;
      line-height: .5;
      border-radius: .2rem;
    }

    .btn-circle {
      width: 30px;
      height: 30px;
      padding: 6px 0;
      border-radius: 15px;
      text-align: center;
      font-size: 12px;
      line-height: 1.428571429;
    }

    .well {
      min-height: 20px;
      padding: 19px;
      margin-bottom: 20px;
      background-color: #f5f5f5;
      border: 1px solid #e3e3e3;
      border-radius: 4px;
      -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, .05);
      box-shadow: inset 0 1px 1px rgba(0, 0, 0, .05);
      font-size: 12px;
    }

    .text-truncate-notify {
      white-space: pre-wrap !important;
    }

    .image-container {
      display: flex;
      align-items: center;
    }

    .image-container .image {
      display: inline-block;
      position: relative;
      width: 32px;
      height: 32px;
      overflow: hidden;
      border-radius: 50%;
      margin-right: 10px;
    }

    .image-container .image img {
      width: auto;
      height: 100%;
    }

    #top-notification-dropdown>a {
      position: relative;
    }

    #top-notification-dropdown>a span {
      position: absolute;
      right: 10%;
      top: 10%;
    }

    #top-notification-dropdown>a span.badge {
      padding: 2px 5px;
    }

    .scrollable {
      max-height: 250px;
      overflow-y: scroll;
    }

    .select2-container {
      width: 100% !important;
    }

    <?php echo $adminTheme->admin_custom_css; ?>

  </style>

  <!-- Google Font: Source Sans Pro -->
  <link href="https://fonts.googleapis.com/css?family=Source+Sans+Pro:300,400,400i,700" rel="stylesheet">
</head>

<body class="hold-transition sidebar-mini">
  <!-- Site wrapper -->
  <div class="wrapper">
    <!-- Navbar -->
    <nav class="main-header navbar navbar-expand navbar-light border-bottom bg-white">
      <!-- Left navbar links -->
      <ul class="navbar-nav">
        <li class="nav-item">
          <a class="nav-link" data-widget="pushmenu" href="#"><i class="fa fa-bars"></i></a>
        </li>
      </ul>
      <!-- Right navbar links -->
      <ul class="navbar-nav ml-auto">
        <li class="nav-item">
          <a <?php if(!$user->is_superadmin): ?> href="<?php echo e(route('admin.profile.index')); ?>"
          <?php else: ?>
            href="<?php echo e(route('superadmin.profile.index')); ?>" <?php endif; ?>
             class="image-container nav-link waves-effect waves-light">
            <div class="image">
              <img alt="User Image" src="<?php echo e($user->profile_image_url); ?>">
            </div>
            <span><?php echo e(ucwords($user->name)); ?></span>
          </a>

        </li>

        <!-- Notifications Dropdown Menu -->
        <li class="nav-item dropdown" id="top-notification-dropdown">
          <a class="nav-link" data-toggle="dropdown" href="#">
            <i class="fa fa-bell-o"></i>
            <?php if(count($user->unreadNotifications) > 0): ?>
              <span class="badge badge-warning navbar-badge"><?php echo e(count($user->unreadNotifications)); ?></span>
            <?php endif; ?>
          </a>
          <div class="dropdown-menu dropdown-menu-lg dropdown-menu-right">
            <div class="scrollable">
              <?php $__currentLoopData = $user->unreadNotifications; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $notification): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <?php echo $__env->make('notifications.' . snake_case(class_basename($notification->type)), \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
              <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </div>
            <?php if(count($user->unreadNotifications) > 0): ?>
              <a class="dropdown-item dropdown-footer" href="javascript:void(0);" id="mark-notification-read"><?php echo app('translator')->get('app.markNotificationRead'); ?> <i class="fa fa-check"></i></a>
            <?php else: ?>
              <a class="dropdown-item dropdown-footer" href="javascript:void(0);"><?php echo app('translator')->get('messages.notificationNotFound'); ?> </a>
            <?php endif; ?>
          </div>
        </li>
        <li class="nav-item">
          <a class="nav-link waves-effect waves-light" href="<?php echo e(route('logout')); ?>"
             onclick="event.preventDefault();
          document.getElementById('logout-form').submit();" title="Logout"><i class="fa fa-power-off"></i>
            <form action="<?php echo e(route('logout')); ?>" id="logout-form" method="POST" style="display: none;">
              <?php echo e(csrf_field()); ?>

            </form>
          </a>

        </li>
      </ul>
    </nav>
    <!-- /.navbar -->

    <?php echo $__env->make('sections.left-sidebar', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

    <!-- Content Wrapper. Contains page content -->
    <div class="content-wrapper">

      <?php echo $__env->make('sections.breadcrumb', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

      <!-- Main content -->
      <section class="content">

        <?php echo $__env->yieldContent('content'); ?>

      </section>
      <!-- /.content -->
    </div>
    <!-- /.content-wrapper -->
    
    <div class="row hidden-xs hidden-sm rounded" id="footer-sticky-notes">
      <div class="col-md-12" id="sticky-note-header">
        <div class="row">
          <div class="col-md-10" style="line-height: 30px">
            <?php echo app('translator')->get('app.stickyNotes'); ?> <span class="badge badge-warning"><?php echo e($stickyNotes->count()); ?></span>
            <a href="#" class="btn btn-outline-success btn-xs ml-3" onclick="showCreateNoteModal()">
              <i class="fa fa-plus"></i> <?php echo app('translator')->get('modules.sticky.addNote'); ?>
            </a>
          </div>
          <div class="col-md-2">
            <a href="#" class="btn btn-default btn-circle ml-2" id="open-sticky-bar"><i class="fa fa-chevron-up"></i></a>
            <a href="#" class="btn btn-default btn-circle ml-2" id="close-sticky-bar" style="display: none;"><i
                 class="fa fa-chevron-down"></i></a>
          </div>
        </div>


      </div>

      <div id="sticky-note-list" style="display: none; width: 100%">
        <?php echo $__env->make('admin.sticky-note.note-ajax', ['stickyNotes' => $stickyNotes], \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>;
      </div>
    </div>
    
    
    <div aria-hidden="true" aria-labelledby="myModalLabel" class="modal fade" id="responsive-modal" role="dialog" style="display: none;" tabindex="-1">
      <div class="modal-dialog">
        <div class="modal-content">
          Loading ...
        </div>
      </div>
    </div>
    

    
    <div aria-hidden="true" aria-labelledby="myModalLabel" class="modal fade bs-modal-lg in" id="application-lg-modal" role="dialog">
      <div class="modal-dialog modal-lg modal-dialog-scrollable" id="modal-data-application">
        <div class="modal-content">
          <div class="modal-header">
            <button aria-hidden="true" class="close" data-dismiss="modal" type="button"></button>
            <span class="caption-subject font-red-sunglo bold uppercase" id="modelHeading"></span>
          </div>
          <div class="modal-body">
            Loading...
          </div>
          <div class="modal-footer">
            <button class="btn btn-danger" data-dismiss="modal" type="button"><i class="fa fa-times"></i> <?php echo app('translator')->get('app.cancel'); ?></button>
            <button class="btn btn-success" type="button"><i class="fa fa-check"></i> <?php echo app('translator')->get('app.save'); ?></button>
          </div>
        </div>
        <!-- /.modal-content -->
      </div>
      <!-- /.modal-dialog -->
    </div>
    

    
    <div aria-hidden="true" aria-labelledby="myModalLabel" class="modal fade bs-modal-md in" id="application-md-modal" role="dialog">
      <div class="modal-dialog modal-md" id="modal-data-application">
        <div class="modal-content">
          <div class="modal-header">
            <button aria-hidden="true" class="close" data-dismiss="modal" type="button"></button>
            <span class="caption-subject font-red-sunglo bold uppercase" id="modelHeading"></span>
          </div>
          <div class="modal-body">
            Loading...
          </div>
          <div class="modal-footer">
            <button class="btn btn-danger" data-dismiss="modal" type="button"><i class="fa fa-times"></i> <?php echo app('translator')->get('app.cancel'); ?></button>
            <button class="btn btn-success" type="button"><i class="fa fa-check"></i> <?php echo app('translator')->get('app.save'); ?></button>
          </div>
        </div>
        <!-- /.modal-content -->
      </div>
      <!-- /.modal-dialog -->
    </div>
    


    <footer class="main-footer">
      &copy; <?php echo e(\Carbon\Carbon::today()->year); ?> <?php echo app('translator')->get('app.by'); ?> <?php echo e($companyName); ?>

    </footer>

    <?php echo $__env->make('sections.right-sidebar', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
  </div>
  <!-- ./wrapper -->

  <!-- jQuery -->
  <script src="<?php echo e(asset('assets/plugins/jquery/jquery.min.js')); ?>"></script>
  <!-- Bootstrap 4 -->
  <script src="<?php echo e(asset('assets/node_modules/popper/popper.min.js')); ?>"></script>
  <script src="<?php echo e(asset('assets/plugins/bootstrap/js/bootstrap.bundle.min.js')); ?>"></script>
  <script src="<?php echo e(asset('assets/plugins/datatables/jquery.dataTables.js')); ?>"></script>
  <script src="<?php echo e(asset('assets/plugins/datatables/dataTables.bootstrap4.js')); ?>"></script>

  <!-- SlimScroll -->
  <script src="<?php echo e(asset('assets/plugins/slimScroll/jquery.slimscroll.min.js')); ?>"></script>
  <!-- FastClick -->
  <script src="<?php echo e(asset('assets/plugins/fastclick/fastclick.js')); ?>"></script>
  <!-- AdminLTE App -->
  <script src="<?php echo e(asset('assets/dist/js/adminlte.min.js')); ?>"></script>

  <script src='https://cdn.jsdelivr.net/npm/bootstrap-select@1.13.0/dist/js/bootstrap-select.min.js'></script>
  <script src="<?php echo e(asset('assets/node_modules/sweetalert/sweetalert.min.js')); ?>"></script>

  <script src="<?php echo e(asset('froiden-helper/helper.js')); ?>"></script>
  <script src="<?php echo e(asset('assets/node_modules/toast-master/js/jquery.toast.js')); ?>"></script>
  <script src="<?php echo e(asset('js/cbpFWTabs.js')); ?>"></script>
  <script src="<?php echo e(asset('assets/plugins/icheck/icheck.min.js')); ?>"></script>
  <script src="<?php echo e(asset('assets/plugins/icheck/icheck.init.js')); ?>"></script>
  <script src="<?php echo e(asset('assets/node_modules/Magnific-Popup-master/dist/jquery.magnific-popup.min.js')); ?>"></script>
  <script src="<?php echo e(asset('assets/node_modules/Magnific-Popup-master/dist/jquery.magnific-popup-init.js')); ?>"></script>

  <script>
    $('body').on('click', '.right-side-toggle', function() {
      $("body").removeClass("control-sidebar-slide-open");
    })

    $(function() {
      $('.selectpicker').selectpicker({
        style: 'btn-info',
        size: 4
      });
    });

    function languageOptions() {
      return {
        processing: "<?php echo app('translator')->get('modules.datatables.processing'); ?>",
        search: "<?php echo app('translator')->get('modules.datatables.search'); ?>",
        lengthMenu: "<?php echo app('translator')->get('modules.datatables.lengthMenu'); ?>",
        info: "<?php echo app('translator')->get('modules.datatables.info'); ?>",
        infoEmpty: "<?php echo app('translator')->get('modules.datatables.infoEmpty'); ?>",
        infoFiltered: "<?php echo app('translator')->get('modules.datatables.infoFiltered'); ?>",
        infoPostFix: "<?php echo app('translator')->get('modules.datatables.infoPostFix'); ?>",
        loadingRecords: "<?php echo app('translator')->get('modules.datatables.loadingRecords'); ?>",
        zeroRecords: "<?php echo app('translator')->get('modules.datatables.zeroRecords'); ?>",
        emptyTable: "<?php echo app('translator')->get('modules.datatables.emptyTable'); ?>",
        paginate: {
          first: "<?php echo app('translator')->get('modules.datatables.paginate.first'); ?>",
          previous: "<?php echo app('translator')->get('modules.datatables.paginate.previous'); ?>",
          next: "<?php echo app('translator')->get('modules.datatables.paginate.next'); ?>",
          last: "<?php echo app('translator')->get('modules.datatables.paginate.last'); ?>",
        },
        aria: {
          sortAscending: "<?php echo app('translator')->get('modules.datatables.aria.sortAscending'); ?>",
          sortDescending: "<?php echo app('translator')->get('modules.datatables.aria.sortDescending'); ?>",
        },
      }
    }

    $('.language-switcher').change(function() {
      var lang = $(this).val();
      $.easyAjax({
        url: '<?php echo e(route('admin.language-settings.change-language')); ?>',
        data: {
          'lang': lang
        },
        success: function(data) {
          if (data.status == 'success') {
            window.location.reload();
          }
        }
      });
    });

    $('#mark-notification-read').click(function() {
      var token = '<?php echo e(csrf_token()); ?>';
      $.easyAjax({
        type: 'POST',
        url: '<?php echo e(route('mark-notification-read')); ?>',
        data: {
          '_token': token
        },
        success: function(data) {
          if (data.status == 'success') {
            $('.top-notifications').remove();
            $('#top-notification-dropdown .notify').remove();
            window.location.reload();
          }
        }
      });

    });

    // $('body').on('click', '.view-notification', function(event) {
    $('.read-notification').click(function() {
      event.preventDefault();
      var id = $(this).data('notification-id');
      //  var href = $(this).attr('href');
      var dataUrl = $(this).data('link');

      $.easyAjax({
        url: "<?php echo e(route('mark_single_notification_read')); ?>",
        type: "POST",
        data: {
          '_token': "<?php echo e(csrf_token()); ?>",
          'id': id
        },
        success: function() {

          if (typeof dataUrl !== 'undefined') {
            window.location = dataUrl;
          }
        }
      });
    });

    function addOrEditStickyNote(id) {
      var url = '';
      var method = 'POST';
      if (id === undefined || id == "" || id == null) {
        url = '<?php echo e(route('admin.sticky-note.store')); ?>'
      } else {

        url = "<?php echo e(route('admin.sticky-note.update', ':id')); ?>";
        url = url.replace(':id', id);
        var stickyID = $('#stickyID').val();
        method = 'PUT'
      }

      var noteText = $('#notetext').val();
      var stickyColor = $('#stickyColor').val();
      $.easyAjax({
        url: url,
        container: '#responsive-modal',
        type: method,
        data: {
          'notetext': noteText,
          'stickyColor': stickyColor,
          '_token': '<?php echo e(csrf_token()); ?>'
        },
        success: function(response) {
          $("#responsive-modal").modal('hide');
          getNoteData();
        }
      })
    }

    // FOR SHOWING FEEDBACK DETAIL IN MODEL
    function showCreateNoteModal() {
      var url = '<?php echo e(route('admin.sticky-note.create')); ?>';
      $.ajaxModal('#responsive-modal', url);

      return false;
    }

    // FOR SHOWING FEEDBACK DETAIL IN MODEL
    function showEditNoteModal(id) {
      var url = '<?php echo e(route('admin.sticky-note.edit', ':id')); ?>';
      url = url.replace(':id', id);
      $.ajaxModal('#responsive-modal', url);
      return false;
    }

    function selectColor(id) {
      $('.icolors li.active ').removeClass('active');
      $('#' + id).addClass('active');
      $('#stickyColor').val(id);

    }

    function deleteSticky(id) {

      swal({
        title: "Are you sure?",
        text: "You will not be able to recover the deleted Sticky Note!",
        type: "warning",
        showCancelButton: true,
        confirmButtonColor: "#DD6B55",
        confirmButtonText: "Yes, delete it!",
        cancelButtonText: "No, cancel please!",
        closeOnConfirm: true,
        closeOnCancel: true
      }, function(isConfirm) {
        if (isConfirm) {

          var url = "<?php echo e(route('admin.sticky-note.destroy', ':id')); ?>";
          url = url.replace(':id', id);

          var token = "<?php echo e(csrf_token()); ?>";

          $.easyAjax({
            type: 'POST',
            url: url,
            data: {
              '_token': token,
              '_method': 'DELETE'
            },
            success: function(response) {
              $('#stickyBox_' + id).hide('slow');
              $("#responsive-modal").modal('hide');
              getNoteData();
            }
          });
        }
      });
    }


    //getting all chat data according to user
    function getNoteData() {

      var url = "<?php echo e(route('admin.sticky-note.index')); ?>";

      $.easyAjax({
        type: 'GET',
        url: url,
        messagePosition: '',
        data: {},
        container: ".noteBox",
        success: function(response) {
          //set notes in box
          $('#sticky-note-list').html(response.stickyNotes);
          $('#sticky-note-header span.badge').html(response.count);
        }
      });
    }

    // search input implementation
    function search($input, doneTypingInterval, type) {
      var $anchor = $input.siblings('a');
      var typingTimer, fn;

      if (type == 'data') {
        fn = loadData;
      }
      if (type == 'table') {
        fn = redrawTable;
      }

      $input.on('keyup', function(e) {
        if ($(this).val() !== '' || ($(this).val().length >= 0 && e.key === 'Backspace')) {
          clearTimeout(typingTimer);
          typingTimer = setTimeout(() => {
            fn();
          }, doneTypingInterval);
        }

        $(this).val() !== '' ? $anchor.removeClass('d-none') : $anchor.addClass('d-none');
      })

      $input.on('keydown', function() {
        clearTimeout(typingTimer);
      });

      $anchor.click(function(e) {
        $(this).siblings('input').val('');
        fn();
        $anchor.addClass('d-none');
        $(this).siblings('input').focus();
      })
    }

    //    sticky notes script
    var stickyNoteOpen = $('#open-sticky-bar');
    var stickyNoteClose = $('#close-sticky-bar');
    var stickyNotes = $('#footer-sticky-notes');
    var viewportHeight = Math.max(document.documentElement.clientHeight, window.innerHeight || 0);
    var stickyNoteHeaderHeight = stickyNotes.height();

    $('#sticky-note-list').css('max-height', viewportHeight - 150);

    stickyNoteOpen.click(function() {
      $('#sticky-note-list').toggle(function() {
        $(this).animate({
          height: (viewportHeight - 150)
        })
      });
      stickyNoteClose.toggle();
      stickyNoteOpen.toggle();
    })

    stickyNoteClose.click(function() {
      $('#sticky-note-list').toggle(function() {
        $(this).animate({
          height: 0
        })
      });
      stickyNoteOpen.toggle();
      stickyNoteClose.toggle();
    })
    $('body').on('click', '.toggle-password', function() {
      var $selector = $(this).parent().find('input.form-control');
      $(this).toggleClass("fa-eye fa-eye-slash");
      var $type = $selector.attr("type") === "password" ? "text" : "password";
      $selector.attr("type", $type);
    });
  </script>

  <?php echo $__env->yieldPushContent('footer-script'); ?>

</body>

</html>
<?php /**PATH E:\laragon\www\recruit-seprocal\resources\views/layouts/app.blade.php ENDPATH**/ ?>