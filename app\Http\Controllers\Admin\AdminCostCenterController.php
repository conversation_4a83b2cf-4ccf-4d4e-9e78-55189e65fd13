<?php

namespace App\Http\Controllers\Admin;

use App\CostCenter;
use App\Helper\Reply;
use Illuminate\Http\Request;
use Yajra\DataTables\DataTables;
use App\Http\Requests\Admin\CostCenter\StoreRequest;

class AdminCostCenterController extends AdminBaseController
{
    public function __construct()
    {
        parent::__construct();
        $this->pageTitle = __('app.cost_center');
        $this->pageIcon = 'icon-user';
    }

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        abort_if(! $this->user->cans('view_cost_center'), 403);

        return view('admin.cost_centers.index', $this->data);
    }

    public function create()
    {
        abort_if(!$this->user->cans('add_cost_center'), 403);

        return view('admin.cost_centers.create', $this->data);
    }


    public function edit($id)
    {
        abort_if(! $this->user->cans('edit_cost_center'), 403);

        $this->cost_center = CostCenter::find($id);
        return view('admin.cost_centers.edit', $this->data);
    }

    public function store(StoreRequest $request)
    {
        abort_if(!$this->user->cans('add_cost_center'), 403);

        $data = $request->all();
        CostCenter::create($data);
        return Reply::redirect(route('admin.cost_centers.index'), __('menu.cost_center') . ' ' . __('messages.createdSuccessfully'));
    }

    public function update(StoreRequest $request, $id)
    {
        abort_if(!$this->user->cans('edit_cost_center'), 403);

        $data = $request->all();
        $costCenter = CostCenter::findOrFail($id);
        $costCenter->update($data);

        $costCenterData = CostCenter::all();
        return Reply::redirect(route('admin.cost_centers.index'), __('menu.cost_center') . ' ' . __('messages.cost_centerUpdated'));
        // return Reply::successWithData(__('messages.cost_centerUpdated'),['data' => $costCenterData]);
    }

    public function destroy($id)
    {
        abort_if(!$this->user->cans('delete_cost_center'), 403);

        CostCenter::destroy($id);

        $costCenterData = CostCenter::all();
        return Reply::successWithData(__('messages.recordDeleted'), ['data' => $costCenterData]);
    }

    public function show($id)
    {
        //
    }

    public function data()
    {
        abort_if(! $this->user->cans('view_cost_center'), 403);

        $costCenters = CostCenter::all();

        return DataTables::of($costCenters)
            ->addColumn('action', function ($row) {
                $action = '';

                if ($this->user->cans('edit_cost_center')) {
                    $action .= '<a href="' . route('admin.cost_centers.edit', [$row->id]) . '" class="btn btn-primary btn-circle"
                      data-toggle="tooltip" onclick="this.blur()" data-original-title="'.__('app.edit').'"><i class="fa fa-pencil" aria-hidden="true"></i></a>';
                }

                if ($this->user->cans('delete_cost_center')) {
                    $action .= ' <a href="#" class="btn btn-danger btn-circle sa-params"
                      data-toggle="tooltip" onclick="this.blur()" data-row-id="' . $row->id . '" data-original-title="'.__('app.delete').'"><i class="fa fa-times" aria-hidden="true"></i></a>';
                }
                return $action;
            })
            ->editColumn('name', function ($row) {
                return ucfirst($row->name);
            })
            ->editColumn('code', function ($row) {
                return ucfirst($row->code);
            })
            ->addIndexColumn()
            ->make(true);
    }

}
