<?php

namespace App\Http\Controllers\Admin;

use App\Signus;
use Carbon\Carbon;
use App\JobApplication;
use App\ApplicationStatus;
use Illuminate\Http\Request;
use Froiden\Envato\Helpers\Reply;
use Illuminate\Support\Facades\DB;
use App\Http\Controllers\Controller;
use Yajra\DataTables\Facades\DataTables;
use App\Http\Requests\Signus\StoreRequest;
use App\Http\Requests\Signus\UpdateRequest;

class AdminSignusController extends AdminBaseController
{
    public function __construct()
    {
        parent::__construct();
        $this->pageTitle = "Filtro del cliente";
        $this->pageIcon = 'icon-doc';
    }

    public function index()
    {
        abort_if(!$this->user->cans('view_signus'), 403);
        $this->candidates = JobApplication::all();
        return view('admin.signus.index', $this->data);
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create(Request $request)
    {
        abort_if(!$this->user->cans('add_signus'), 403);
        $this->candidates = JobApplication::all();
        $this->currentCandidateId = $request->id ?? null;
        return view('admin.signus.create', $this->data)->render();
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(StoreRequest $request)
    {
        abort_if(!$this->user->cans('add_signus'), 403);
        $jobApplication = JobApplication::find($request->candidate);
        $signus = Signus::updateOrCreate([
            'job_application_id'   => $request->candidate,
            'name' => $request->name,
        ], [
            'inscription_date'   => $request->inscription_date,
            'signus_date'        => $request->signus_date,
            'status'             => $request->status,
            'comments'           => $request->comments,
        ]);
        $signus->wasRecentlyCreated ? $message = __('messages.createdSuccessfully') : $message = __('messages.updatedSuccessfully');

        // return response()->json($signus);
        return Reply::success(__('menu.internal_filter') . ' ' . $message, json_encode($signus));

        // return Reply::redirect(route('admin.signus.index'), __('menu.signus') . ' ' . $message);
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        abort_if(!$this->user->cans('view_signus'), 403);
        $this->signus = Signus::with(['jobApplication'])->find($id);
        return view('admin.signus.show', $this->data)->render();
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        abort_if(!$this->user->cans('edit_signus'), 403);
        $this->candidates = JobApplication::all();
        $this->signus = Signus::with(['jobApplication'])->find($id);
        return view('admin.signus.edit', $this->data)->render();
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(UpdateRequest $request, $id)
    {
        abort_if(!$this->user->cans('add_signus'), 403);
        $jobApplication = JobApplication::find($request->candidate);
        $signus = Signus::findOrFail($id);
        $signus->name = $request->name;
        $signus->job_application_id = $request->candidate;
        $signus->signus_date = $request->signus_date;
        $signus->inscription_date = $request->inscription_date;
        $signus->status = $request->status;
        $signus->comments = $request->comments;
        $signus->save();


        return Reply::success(__('menu.signus') . ' ' . __('messages.updatedSuccessfully'), json_encode($signus));
        // return Reply::redirect(route('admin.signus.index'), __('menu.signus') . ' ' . __('messages.updatedSuccessfully'));
    }
    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        abort_if(!$this->user->cans('delete_signus'), 403);

        Signus::destroy($id);
        return Reply::success(__('messages.recordDeleted'));
    }

    /**
     * @param Request $request
     * @return mixed
     * @throws \Exception
     */
    public function data(Request $request)
    {
        abort_if(!$this->user->cans('view_signus'), 403);

        $query = Signus::select('signus.id', 'job_applications.full_name as full_name', 'signus.status', 'signus.inscription_date', 'signus.signus_date')
            ->leftjoin('job_applications', 'job_applications.id', 'signus.job_application_id');

        // Filter by status
        if ($request->status != 'all' && $request->status != '') {
            $query = $query->where('signus.status', $request->status);
        }

        // Filter By candidate
        if ($request->applicationID != 'all' && $request->applicationID != '') {
            $query = $query->where('job_applications.id', $request->applicationID);
        }

        // Filter by StartDate
        if ($request->startDate !== null && $request->startDate != 'null') {
            $query = $query->where(DB::raw('DATE(signus.`signus_date`)'), '>=', "$request->startDate");
        }

        // Filter by EndDate
        if ($request->endDate !== null && $request->endDate != 'null') {
            $query = $query->where(DB::raw('DATE(signus.`signus_date`)'), '<=', "$request->endDate");
        }

        return DataTables::of($query)
            ->addColumn('action', function ($row) {
                $action = '';
                if ($this->user->cans('view_signus')) {
                    $action .= '<a href="#" data-row-id="' . $row->id . '" class="btn btn-info btn-circle view-data"
                      data-toggle="tooltip" onclick="this.blur()" data-original-title="' . __('app.view') . '"><i class="fa fa-search" aria-hidden="true"></i></a>';
                }
                if ($this->user->cans('edit_signus')) {
                    $action .= '<a href="#" style="margin-left:4px" data-row-id="' . $row->id . '" class="btn btn-primary btn-circle edit-data"
                      data-toggle="tooltip" onclick="this.blur()" data-original-title="' . __('app.edit') . '"><i class="fa fa-pencil" aria-hidden="true"></i></a>';
                }

                if ($this->user->cans('delete_signus')) {
                    $action .= ' <a href="#" class="btn btn-danger btn-circle sa-params"
                      data-toggle="tooltip" onclick="this.blur()" data-row-id="' . $row->id . '" data-original-title="' . __('app.delete') . '"><i class="fa fa-times" aria-hidden="true"></i></a>';
                }
                return $action;
            })
            ->addColumn('checkbox', function ($row) {
                return '
                    <div class="checkbox form-check">
                        <input id="' . $row->id . '" type="checkbox" name="id[]" class="form-check-input" value="' . $row->id . '" >
                        <label for="' . $row->id . '"></label>
                    </div>
                ';
            })
            ->editColumn('job_applications.full_name', function ($row) {
                return ucwords($row->full_name);
            })
            ->editColumn('inscription_date', function ($row) {
                Carbon::setlocale(config('app.locale'));
                return is_null($row->inscription_date) ? null : Carbon::parse($row->inscription_date)->translatedFormat('d \de F \de Y');
            })
            ->editColumn('signus_date', function ($row) {
                Carbon::setlocale(config('app.locale'));
                return is_null($row->signus_date) ? null : Carbon::parse($row->signus_date)->translatedFormat('d \de F \de Y');
            })
            ->editColumn('status', function ($row) {
                if ($row->status == 'pending') {
                    return '<label class="badge bg-warning">' . __('app.pending') . '</label>';
                }
                if ($row->status == 'suitable') {
                    return '<label class="badge bg-success">' . __('app.suitable') . '</label>';
                }
                if ($row->status == 'observed') {
                    return '<label class="badge bg-danger">' . __('app.observed') . '</label>';
                }
            })
            ->rawColumns(['action', 'status', 'job_applications.full_name', 'checkbox'])
            ->make(true);
    }

    public function dataByCandidate(Request $request, $id)
    {
        abort_if(!$this->user->cans('view_signus'), 403);

        $query = Signus::select('signus.id', 'signus.name', 'signus.status', 'signus.signus_date', 'signus.inscription_date')
            ->leftjoin('job_applications', 'job_applications.id', 'signus.job_application_id')
            ->where('signus.job_application_id', $id);

        return DataTables::of($query)
            ->addColumn('action', function ($row) {
                $action = '';
                if ($this->user->cans('view_signus')) {
                    $action .= '<a href="#" class="btn btn-info btn-circle view-data"
                    data-toggle="tooltip" onclick="showSignus(' . $row->id . ')" data-original-title="' . __('app.view') . '"><i class="fa fa-search" aria-hidden="true"></i></a>';
                }
                if ($this->user->cans('edit_signus')) {
                    $action .= '<a href="#" style="margin-left:4px" class="btn btn-primary btn-circle edit-data"
                    data-toggle="tooltip" onclick="editSignus(' . $row->id . ')" data-original-title="' . __('app.edit') . '"><i class="fa fa-pencil" aria-hidden="true"></i></a>';
                }

                return $action;
            })
            ->editColumn('signus_date', function ($row) {
                Carbon::setlocale(config('app.locale'));
                return is_null($row->signus_date) ? null : Carbon::parse($row->signus_date)->translatedFormat('d \de F \de Y');
            })
            ->editColumn('inscription_date', function ($row) {
                Carbon::setlocale(config('app.locale'));
                return is_null($row->inscription_date) ? null : Carbon::parse($row->inscription_date)->translatedFormat('d \de F \de Y');
            })
            ->editColumn('status', function ($row) {
                if ($row->status == 'pending') {
                    return '<label class="badge bg-warning">' . __('app.pending') . '</label>';
                }
                if ($row->status == 'suitable') {
                    return '<label class="badge bg-success">' . __('app.suitable') . '</label>';
                }
                if ($row->status == 'observed') {
                    return '<label class="badge bg-danger">' . __('app.observed') . '</label>';
                }
            })
            ->rawColumns(['action', 'status'])
            ->make(true);
    }
}
