<?php

namespace App;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Signus extends Model
{
    use HasFactory;

    protected $table = 'signus';

    protected $fillable = [
        'name',
        'inscription_date',
        'signus_date',
        'status',
        'comments',
        'job_application_id',
    ];

    protected $casts = [
        'inscription_date' => 'datetime:Y-m-d',
        'signus_date' => 'datetime:Y-m-d',
    ];

    public function jobApplication()
    {
        return $this->belongsTo(JobApplication::class);
    }
}
