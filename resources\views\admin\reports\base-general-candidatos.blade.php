@extends('layouts.app')

@push('head-script')
    <link href="//cdn.datatables.net/fixedheader/3.1.5/css/fixedHeader.bootstrap.min.css" rel="stylesheet">
    <link href="//cdn.datatables.net/responsive/2.2.3/css/responsive.bootstrap.min.css" rel="stylesheet">
    <link href="//cdn.datatables.net/buttons/1.0.3/css/buttons.dataTables.min.css" rel="stylesheet">
@endpush

@section('page-title')
    <div class="row bg-title">
        <div class="col-lg-3 col-md-4 col-sm-4 col-xs-12">
            <h4 class="page-title"><i class="{{ $pageIcon }}"></i> {{ $pageTitle }}</h4>
        </div>
        <div class="col-lg-9 col-sm-8 col-md-8 col-xs-12">
            <ol class="breadcrumb">
                <li><a href="{{ route('admin.dashboard') }}">@lang('app.menu.home')</a></li>
                <li><a href="{{ route('admin.reports.index') }}">@lang('menu.reports')</a></li>
                <li class="active">{{ $pageTitle }}</li>
            </ol>
        </div>
    </div>
@endsection

@section('content')
    <div class="row">
        <div class="col-md-12">
            <div class="white-box">
                <h3 class="box-title">{{ $pageTitle }}</h3>
                <p class="text-muted">Reporte completo de todos los candidatos del sistema con información de unidad minera, centro de costo, puesto, categoría, datos iniciales, etapa actual, fecha de transición y responsable del proceso.</p>

                <!-- Filtros de fecha -->
                <div class="row mb-3">
                    <div class="col-md-12">
                        <div class="form-group">
                            <label class="control-label">Filtrar por rango de fechas de aplicación:</label>
                            <div class="row">
                                <div class="col-md-3">
                                    <input type="date" id="start_date" class="form-control" placeholder="Fecha inicio">
                                </div>
                                <div class="col-md-3">
                                    <input type="date" id="end_date" class="form-control" placeholder="Fecha fin">
                                </div>
                                <div class="col-md-3">
                                    <button type="button" id="filter_btn" class="btn btn-primary">Filtrar</button>
                                    <button type="button" id="reset_btn" class="btn btn-default">Limpiar</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="table-responsive">
                    <table id="myTable" class="table table-bordered table-striped">
                        <thead>
                            <tr>
                                <th>#</th>
                                <th>Datos Personales</th>
                                <th>Unidad Minera / Centro de Costo</th>
                                <th>Puesto / Categoría</th>
                                <th>Etapa / Estado</th>
                                <th>Fecha de Etapa</th>
                                <th>Responsable del Proceso</th>
                            </tr>
                        </thead>
                    </table>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('footer-script')
    <script src="{{ asset('assets/node_modules/moment/moment.js') }}" type="text/javascript"></script>
    <script src="//cdn.datatables.net/fixedheader/3.1.5/js/dataTables.fixedHeader.min.js"></script>
    <script src="//cdn.datatables.net/responsive/2.2.3/js/dataTables.responsive.min.js"></script>
    <script src="//cdn.datatables.net/responsive/2.2.3/js/responsive.bootstrap.min.js"></script>
    <script src="//cdn.datatables.net/buttons/1.0.3/js/dataTables.buttons.min.js"></script>
    <script src="//cdn.datatables.net/buttons/1.0.3/js/buttons.bootstrap.min.js"></script>
    <script src="//cdnjs.cloudflare.com/ajax/libs/jszip/2.5.0/jszip.min.js"></script>
    <script src="//cdn.rawgit.com/bpampuch/pdfmake/0.1.18/build/pdfmake.min.js"></script>
    <script src="//cdn.rawgit.com/bpampuch/pdfmake/0.1.18/build/vfs_fonts.js"></script>
    <script src="//cdn.datatables.net/buttons/1.0.3/js/buttons.html5.min.js"></script>

    <script>
        var table = $('#myTable').dataTable({
            responsive: true,
            processing: true,
            serverSide: true,
            ajax: {
                url: '{!! route('admin.reports.base-general-candidatos.data') !!}',
                data: function (d) {
                    d.start_date = $('#start_date').val();
                    d.end_date = $('#end_date').val();
                }
            },
            language: languageOptions(),
            "fnDrawCallback": function(oSettings) {
                $("body").tooltip({
                    selector: '[data-toggle="tooltip"]'
                });
            },
            dom: 'Bfrtip',
            buttons: [
                {
                    text: 'Excel',
                    className: 'btn btn-success',
                    action: function(e, dt, button, config) {
                        var startDate = $('#start_date').val();
                        var endDate = $('#end_date').val();
                        var url = '{{ route("admin.reports.base-general-candidatos.export") }}';

                        if (startDate || endDate) {
                            url += '?';
                            if (startDate) url += 'start_date=' + startDate;
                            if (startDate && endDate) url += '&';
                            if (endDate) url += 'end_date=' + endDate;
                        }

                        window.location.href = url;
                    }
                },
                {
                    text: 'PDF',
                    className: 'btn btn-danger',
                    action: function(e, dt, button, config) {
                        var startDate = $('#start_date').val();
                        var endDate = $('#end_date').val();
                        var url = '{{ route("admin.reports.base-general-candidatos.export-pdf") }}';

                        if (startDate || endDate) {
                            url += '?';
                            if (startDate) url += 'start_date=' + startDate;
                            if (startDate && endDate) url += '&';
                            if (endDate) url += 'end_date=' + endDate;
                        }

                        window.location.href = url;
                    }
                }
            ],
            columns: [
                {data: 'DT_Row_Index', name: 'DT_Row_Index', orderable: false, searchable: false},
                {data: 'datos_personales', name: 'full_name'},
                {data: 'unidad_centro_costo', name: 'unidad_minera'},
                {data: 'puesto_categoria', name: 'puesto'},
                {data: 'etapa_estado', name: 'titulo_etapa'},
                {data: 'fecha_etapa', name: 'updated_at'},
                {data: 'responsable_proceso', name: 'responsable_proceso'}
            ]
        });

        new $.fn.dataTable.FixedHeader(table);

        // Filtros de fecha
        $('#filter_btn').on('click', function() {
            table.fnDraw();
        });

        $('#reset_btn').on('click', function() {
            $('#start_date').val('');
            $('#end_date').val('');
            table.fnDraw();
        });
    </script>
@endpush
