<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title><?php echo e($title); ?></title>
    <style>
        body { font-family: Arial, sans-serif; font-size: 10px; margin: 20px; }
        .header { text-align: center; margin-bottom: 20px; border-bottom: 2px solid #333; padding-bottom: 10px; }
        .title { font-size: 16px; font-weight: bold; margin-bottom: 5px; }
        .date { font-size: 12px; color: #666; }
        table { width: 100%; border-collapse: collapse; margin-top: 10px; }
        th, td { border: 1px solid #ddd; padding: 6px; text-align: left; vertical-align: top; }
        th { background-color: #f2f2f2; font-weight: bold; font-size: 9px; }
        td { font-size: 8px; line-height: 1.2; }
        .footer { margin-top: 20px; text-align: center; font-size: 8px; color: #666; }
        .small-text { font-size: 7px; }
    </style>
</head>
<body>
    <div class="header">
        <div class="title"><?php echo e($title); ?></div>
        <div class="date">Generado el: <?php echo e(\Carbon\Carbon::now()->format('d/m/Y H:i')); ?></div>
    </div>

    <table>
        <thead>
            <tr>
                <th width="5%">#</th>
                <th width="22%">Datos Personales</th>
                <th width="18%">Unidad / Centro Costo</th>
                <th width="18%">Puesto / Categoría</th>
                <th width="12%">Etapa / Estado</th>
                <th width="12%">Fecha Etapa</th>
                <th width="13%">Responsable</th>
            </tr>
        </thead>
        <tbody>
            <?php $__currentLoopData = $data; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $row): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <tr>
                <td><?php echo e($index + 1); ?></td>
                <td class="small-text">
                    <strong><?php echo e($row->full_name); ?></strong><br>
                    <?php echo e($row->email); ?><br>
                    <?php echo e($row->phone); ?><br>
                    <?php echo e($row->gender); ?>

                    <?php if($row->dob): ?>
                    <br><?php echo e(\Carbon\Carbon::parse($row->dob)->format('d/m/Y')); ?>

                    <?php endif; ?>
                </td>
                <td class="small-text">
                    <strong><?php echo e($row->unidad_minera ?? 'N/A'); ?></strong><br>
                    <?php echo e($row->centro_costo ?? 'N/A'); ?>

                    <?php if($row->codigo_centro_costo): ?>
                    <br>(<?php echo e($row->codigo_centro_costo); ?>)
                    <?php endif; ?>
                </td>
                <td class="small-text">
                    <strong><?php echo e($row->puesto); ?></strong>
                    <?php if($row->descripcion_puesto): ?>
                    <br><?php echo e($row->descripcion_puesto); ?>

                    <?php endif; ?>
                    <br><em><?php echo e($row->categoria_puesto ? (in_array(strtolower($row->categoria_puesto), ['operario', 'obrero', 'técnico', 'operador', 'ayudante']) ? 'OBRERO' : 'EMPLEADO') : 'N/A'); ?></em>
                </td>
                <td class="small-text"><?php echo e($row->titulo_etapa ?? $row->etapa_actual ?? 'N/A'); ?></td>
                <td class="small-text"><?php echo e(\Carbon\Carbon::parse($row->updated_at)->format('d/m/Y')); ?></td>
                <td class="small-text"><?php echo e($row->responsable_proceso ?? 'N/A'); ?></td>
            </tr>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </tbody>
    </table>

    <div class="footer">
        <p>Total de registros: <?php echo e(count($data)); ?> | Reporte generado por Sistema de Reclutamiento SEPROCAL</p>
    </div>
</body>
</html>
<?php /**PATH E:\laragon\www\recruit-seprocal\resources\views/admin/reports/pdf/base-general-candidatos.blade.php ENDPATH**/ ?>