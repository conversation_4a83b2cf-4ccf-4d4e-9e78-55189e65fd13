<?php

namespace App\Http\Requests;

use App\Job;
use App\Question;
use App\JobApplication;
use Illuminate\Support\Arr;
use Illuminate\Foundation\Http\FormRequest;

class UpdateJobApplication extends CoreRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        $job = Job::where('id', $this->job_id)->first();
        $application = JobApplication::select('id', 'job_id', 'photo')->with(['resumeDocument'])->where('id', $this->route('job_application'))->first();
        $requiredColumns = $job->required_columns;
        $sectionVisibility = $job->section_visibility;

        $rules = [
            'first_name' => 'required',
            'last_name' => 'required',
            'email' => 'required',
            'phone' => 'required',
            'job_id' => 'required|exists:jobs,id',
            'gender' => 'required|in:male,female,others',
            'dob' => 'required|date',
            'resume' => 'sometimes|required|mimes:jpeg,jpg,png,doc,docx,rtf,xls,xlsx,pdf',
        ];

        $rules = Arr::add($rules, 'resume', 'required|mimes:jpeg,jpg,png,doc,docx,rtf,xls,xlsx,pdf');

        if (!empty($this->get('answer'))) {
            foreach ($this->get('answer') as $key => $value) {

                $answer = Question::where('id', $key)->first();
                if ($answer->required == 'yes') {
                    $rules["answer.{$key}"] = 'required';
                }
            }
        }

        return $rules;
    }

    public function messages()
    {
        return [
            'answer.*.required' => 'This answer field is required.',
            'dob.required' => 'La fecha de nacimiento es obligatoria.',
            'country.min' => 'Please select country.',
            'state.min' => 'Please select state.',
            'city.required' => 'Please enter city.',
            'resume.required' => 'Debe adjuntar su currículum.',
        ];
    }
}
