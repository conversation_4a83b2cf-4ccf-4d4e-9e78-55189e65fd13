<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>{{ $title }}</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            font-size: 10px;
            margin: 20px;
        }
        .header {
            text-align: center;
            margin-bottom: 20px;
            border-bottom: 2px solid #333;
            padding-bottom: 10px;
        }
        .title {
            font-size: 16px;
            font-weight: bold;
            margin-bottom: 5px;
        }
        .date {
            font-size: 12px;
            color: #666;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 10px;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 6px;
            text-align: left;
            vertical-align: top;
        }
        th {
            background-color: #f2f2f2;
            font-weight: bold;
            font-size: 9px;
        }
        td {
            font-size: 8px;
            line-height: 1.2;
        }
        .footer {
            margin-top: 20px;
            text-align: center;
            font-size: 8px;
            color: #666;
        }
        .small-text {
            font-size: 7px;
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="title">{{ $title }}</div>
        <div class="date">Generado el: {{ \Carbon\Carbon::now()->format('d/m/Y H:i') }}</div>
    </div>

    <table>
        <thead>
            <tr>
                <th width="5%">#</th>
                <th width="20%">Datos Personales</th>
                <th width="15%">Unidad / Centro Costo</th>
                <th width="15%">Puesto / Categoría</th>
                <th width="10%">Etapa Actual</th>
                <th width="10%">Fecha Etapa</th>
                <th width="15%">Condiciones</th>
                <th width="10%">Responsable</th>
            </tr>
        </thead>
        <tbody>
            @foreach($data as $index => $row)
            <tr>
                <td>{{ $index + 1 }}</td>
                <td class="small-text">
                    <strong>{{ $row->full_name }}</strong><br>
                    {{ $row->email }}<br>
                    {{ $row->phone }}<br>
                    {{ $row->gender }}
                    @if($row->dob)
                    <br>{{ \Carbon\Carbon::parse($row->dob)->format('d/m/Y') }}
                    @endif
                </td>
                <td class="small-text">
                    <strong>{{ $row->unidad_minera ?? 'N/A' }}</strong><br>
                    {{ $row->centro_costo ?? 'N/A' }}
                    @if($row->codigo_centro_costo)
                    <br>({{ $row->codigo_centro_costo }})
                    @endif
                </td>
                <td class="small-text">
                    <strong>{{ $row->puesto }}</strong>
                    @if($row->descripcion_puesto)
                    <br>{{ $row->descripcion_puesto }}
                    @endif
                    <br><em>{{ $row->categoria_puesto ? (in_array(strtolower($row->categoria_puesto), ['operario', 'obrero', 'técnico', 'operador', 'ayudante']) ? 'OBRERO' : 'EMPLEADO') : 'N/A' }}</em>
                </td>
                <td class="small-text">{{ $row->titulo_etapa ?? $row->etapa_actual ?? 'N/A' }}</td>
                <td class="small-text">{{ \Carbon\Carbon::parse($row->updated_at)->format('d/m/Y') }}</td>
                <td class="small-text">
                    {{ $row->pay_type ?? 'N/A' }}<br>
                    @if($row->starting_salary)
                    S/ {{ $row->starting_salary }}
                    @if($row->maximum_salary && $row->maximum_salary != $row->starting_salary)
                    - S/ {{ $row->maximum_salary }}
                    @endif
                    @else
                    No especificado
                    @endif
                </td>
                <td class="small-text">{{ $row->responsable_proceso ?? 'N/A' }}</td>
            </tr>
            @endforeach
        </tbody>
    </table>

    <div class="footer">
        <p>Total de registros: {{ count($data) }} | Reporte generado por Sistema de Reclutamiento SEPROCAL</p>
    </div>
</body>
</html>
