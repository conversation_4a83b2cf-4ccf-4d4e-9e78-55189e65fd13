<?php $__currentLoopData = $stickyNotes; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $note): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
    <div class="col-md-12 sticky-note" id="stickyBox_<?php echo e($note->id); ?>">
        <div class="well bg-<?php echo e($note->colour); ?> b-none">
            <p><?php echo nl2br($note->note_text); ?></p>
            <hr>
            <div class="row font-12">
                <div class="col-md-12">
                    <?php echo app('translator')->get("modules.sticky.lastUpdated"); ?>: <?php echo e($note->updated_at->diffForHumans()); ?>

                </div>
                <div class="col-md-12 mt-2">
                    <a href="#" class="text-white" onclick="showEditNoteModal(<?php echo e($note->id); ?>)"><i class="ti-pencil-alt"></i> <?php echo app('translator')->get('app.edit'); ?></a>
                    <a href="#" class="text-white ml-2" onclick="deleteSticky(<?php echo e($note->id); ?>)" ><i class="ti-close"></i> <?php echo app('translator')->get('app.delete'); ?></a>
                </div>
            </div>
        </div>
    </div>
<?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
<?php /**PATH E:\laragon\www\recruit-seprocal\resources\views/admin/sticky-note/note-ajax.blade.php ENDPATH**/ ?>