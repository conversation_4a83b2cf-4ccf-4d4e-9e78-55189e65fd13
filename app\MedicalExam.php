<?php

namespace App;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class MedicalExam extends Model
{
    use HasFactory;

    protected $fillable = [
        'medical_exam_date',
        'status',
        'comments',
        'clinic_id',
        'job_application_id',

    ];

    protected $casts = [
        'medical_exam_date' => 'datetime:Y-m-d',
    ];

    public function jobApplication()
    {
        return $this->belongsTo(JobApplication::class);
    }

    public function clinic()
    {
        return $this->belongsTo(Clinic::class);
    }
    
    public function documents()
    {
        return $this->morphMany(Document::class, 'documentable');
    }
}
