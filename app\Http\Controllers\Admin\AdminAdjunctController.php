<?php

namespace App\Http\Controllers\Admin;

use App\Adjunct;
use Carbon\Carbon;
use App\JobApplication;
use App\ApplicationStatus;
use Illuminate\Http\Request;
use Froiden\Envato\Helpers\Reply;
use Illuminate\Support\Facades\DB;
use App\Http\Controllers\Controller;
use Yajra\DataTables\Facades\DataTables;
use App\Http\Requests\Adjunct\StoreRequest;
use App\Http\Requests\Adjunct\UpdateRequest;

class AdminAdjunctController extends AdminBaseController
{
    public function __construct()
    {
        parent::__construct();
        $this->pageTitle = 'Anexo 4';
        $this->pageIcon = 'icon-doc';
    }

    public function index()
    {
        abort_if(!$this->user->cans('view_adjunct'), 403);
        $this->candidates = JobApplication::all();
        return view('admin.adjunct.index', $this->data);
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create(Request $request)
    {
        abort_if(!$this->user->cans('add_adjunct'), 403);
        $this->candidates = JobApplication::all();
        $this->currentCandidateId = $request->id ?? null;
        return view('admin.adjunct.create', $this->data)->render();
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(StoreRequest $request)
    {
        abort_if(!$this->user->cans('add_adjunct'), 403);
        $jobApplication = JobApplication::find($request->candidate);
        $adjunct = Adjunct::updateOrCreate([
            'job_application_id'   => $request->candidate,
        ], [
            'adjunct_date'  => $request->adjunct_date,
            'status'        => $request->status,
            'local'         => $request->local,
            'comments'      => $request->comments,
        ]);
        $adjunct->wasRecentlyCreated ? $message = __('messages.createdSuccessfully') : $message = __('messages.updatedSuccessfully');
        if ($adjunct->status == 'suitable' && $jobApplication->signus?->status == 'suitable' && $jobApplication->status->status == 'habilitación mina') {
            $jobApplication->status_id = ApplicationStatus::where('status', 'oferta de trabajo')->first()->id;
            $jobApplication->column_priority = -999;
            $jobApplication->save();
        } elseif ($jobApplication->status->status != 'habilitación mina') {
            $jobApplication->status_id = ApplicationStatus::where('status', 'habilitación mina')->first()->id;
            $jobApplication->column_priority = -999;
            $jobApplication->save();
        }

        // return response()->json($adjunct);
        return Reply::success(__('menu.adjunct') . ' ' . $message, json_encode($adjunct));

        // return Reply::redirect(route('admin.adjunct.index'), __('menu.adjunct') . ' ' . $message);
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        abort_if(!$this->user->cans('view_adjunct'), 403);
        $this->adjunct = Adjunct::with(['jobApplication'])->find($id);
        return view('admin.adjunct.show', $this->data)->render();
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        abort_if(!$this->user->cans('edit_adjunct'), 403);
        $this->candidates = JobApplication::all();
        $this->adjunct = Adjunct::with(['jobApplication'])->find($id);
        return view('admin.adjunct.edit', $this->data)->render();
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(UpdateRequest $request, $id)
    {
        abort_if(!$this->user->cans('add_adjunct'), 403);
        $adjunct = Adjunct::findOrFail($id);
        $jobApplication = JobApplication::find($request->candidate);

        $adjunct->job_application_id = $request->candidate;
        $adjunct->adjunct_date = $request->adjunct_date;
        $adjunct->local = $request->local;
        $adjunct->status = $request->status;
        $adjunct->comments = $request->comments;
        $adjunct->save();
        if ($adjunct->status == 'suitable' && $jobApplication->signus?->status == 'suitable' && $jobApplication->status->status == 'habilitación mina') {
            $min_column_prority = JobApplication::where('status_id', ApplicationStatus::where('status', 'oferta de trabajo')->pluck('id'))->min('column_priority');
            $jobApplication->column_priority = $min_column_prority - 1;
            $jobApplication->status_id = ApplicationStatus::where('status', 'oferta de trabajo')->first()->id;
            $jobApplication->save();
        } elseif ($jobApplication->status->status != 'habilitación mina') {
            $min_column_prority = JobApplication::where('status_id', ApplicationStatus::where('status', 'habilitación mina')->pluck('id'))->min('column_priority');
            $jobApplication->column_priority = $min_column_prority - 1;
            $jobApplication->status_id = ApplicationStatus::where('status', 'habilitación mina')->first()->id;
            $jobApplication->save();
        }

        return Reply::success(__('menu.adjunct') . ' ' . __('messages.updatedSuccessfully'), json_encode($adjunct));
        // return Reply::redirect(route('admin.adjunct.index'), __('menu.adjunct') . ' ' . __('messages.updatedSuccessfully'));
    }
    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        abort_if(!$this->user->cans('delete_adjunct'), 403);

        Adjunct::destroy($id);
        return Reply::success(__('messages.recordDeleted'));
    }

    /**
     * @throws \Exception
     */
    public function data(Request $request)
    {
        abort_if(!$this->user->cans('view_adjunct'), 403);

        $query = Adjunct::selectRaw('adjuncts.id, job_applications.full_name, adjuncts.status, adjuncts.local, adjuncts.adjunct_date')
            ->leftjoin('job_applications', 'job_applications.id', 'adjuncts.job_application_id');

        // Filter by status
        if ($request->status != 'all' && $request->status != '') {
            $query = $query->where('adjunct.status', $request->status);
        }

        // Filter By candidate
        if ($request->applicationID != 'all' && $request->applicationID != '') {
            $query = $query->where('job_applications.id', $request->applicationID);
        }

        // Filter by StartDate
        if ($request->startDate !== null && $request->startDate != 'null') {
            $query = $query->where(DB::raw('DATE(adjunct.`adjunct_date`)'), '>=', "{$request->startDate}");
        }

        // Filter by EndDate
        if ($request->endDate !== null && $request->endDate != 'null') {
            $query = $query->where(DB::raw('DATE(adjunct.`adjunct_date`)'), '<=', "{$request->endDate}");
        }

        return DataTables::of($query)
            ->addColumn('action', function ($row) {
                $action = '';
                if ($this->user->cans('view_adjunct')) {
                    $action .= '<a href="#" data-row-id="' . $row->id . '" class="btn btn-info btn-circle view-data"
                      data-toggle="tooltip" onclick="this.blur()" data-original-title="' . __('app.view') . '"><i class="fa fa-search" aria-hidden="true"></i></a>';
                }
                if ($this->user->cans('edit_adjunct')) {
                    $action .= '<a href="#" style="margin-left:4px" data-row-id="' . $row->id . '" class="btn btn-primary btn-circle edit-data"
                      data-toggle="tooltip" onclick="this.blur()" data-original-title="' . __('app.edit') . '"><i class="fa fa-pencil" aria-hidden="true"></i></a>';
                }

                if ($this->user->cans('delete_adjunct')) {
                    $action .= ' <a href="#" class="btn btn-danger btn-circle sa-params"
                      data-toggle="tooltip" onclick="this.blur()" data-row-id="' . $row->id . '" data-original-title="' . __('app.delete') . '"><i class="fa fa-times" aria-hidden="true"></i></a>';
                }
                return $action;
            })
            ->addColumn('checkbox', function ($row) {
                return '
                    <div class="checkbox form-check">
                        <input id="' . $row->id . '" type="checkbox" name="id[]" class="form-check-input" value="' . $row->id . '" >
                        <label for="' . $row->id . '"></label>
                    </div>
                ';
            })
            ->editColumn('local', function ($row) {
                return ucwords($row->local);
            })
            ->editColumn('job_applications.full_name', function ($row) {
                return ucwords($row->full_name);
            })
            ->editColumn('adjunct_date', function ($row) {
                Carbon::setlocale(config('app.locale'));
                return is_null($row->adjunct_date) ? null : Carbon::parse($row->adjunct_date)->translatedFormat('d \de F \de Y');
            })
            ->editColumn('status', function ($row) {
                if ($row->status == 'pending') {
                    return '<label class="badge bg-warning">' . __('app.pending') . '</label>';
                }
                if ($row->status == 'suitable') {
                    return '<label class="badge bg-success">' . __('app.suitable') . '</label>';
                }
                if ($row->status == 'observed') {
                    return '<label class="badge bg-danger">' . __('app.observed') . '</label>';
                }
            })
            ->rawColumns(['action', 'status', 'job_applications.full_name', 'checkbox'])
            ->make(true);
    }

    public function dataByCandidate(Request $request, $id)
    {
        abort_if(!$this->user->cans('view_adjunct'), 403);

        $query = Adjunct::select('adjuncts.id', 'adjuncts.status', 'adjuncts.local', 'adjuncts.adjunct_date')
            ->leftjoin('job_applications', 'job_applications.id', 'adjuncts.job_application_id')
            ->where('adjuncts.job_application_id', $id);

        return DataTables::of($query)
            ->addColumn('action', function ($row) {
                $action = '';
                if ($this->user->cans('view_adjunct')) {
                    $action .= '<a href="#" class="btn btn-info btn-circle view-data"
                    data-toggle="tooltip" onclick="showAdjunct(' . $row->id . ')" data-original-title="' . __('app.view') . '"><i class="fa fa-search" aria-hidden="true"></i></a>';
                }
                if ($this->user->cans('edit_adjunct')) {
                    $action .= '<a href="#" style="margin-left:4px" class="btn btn-primary btn-circle edit-data"
                    data-toggle="tooltip" onclick="editAdjunct(' . $row->id . ')" data-original-title="' . __('app.edit') . '"><i class="fa fa-pencil" aria-hidden="true"></i></a>';
                }

                return $action;
            })
            ->editColumn('adjunct_date', function ($row) {
                Carbon::setlocale(config('app.locale'));

                return is_null($row->adjunct_date) ? null : Carbon::parse($row->adjunct_date)->translatedFormat('d \de F \de Y');
            })
            ->editColumn('local', function ($row) {
                return ucwords($row->local);
            })
            ->editColumn('status', function ($row) {
                if ($row->status == 'pending') {
                    return '<label class="badge bg-warning">' . __('app.pending') . '</label>';
                }
                if ($row->status == 'suitable') {
                    return '<label class="badge bg-success">' . __('app.suitable') . '</label>';
                }
                if ($row->status == 'observed') {
                    return '<label class="badge bg-danger">' . __('app.observed') . '</label>';
                }
            })
            ->rawColumns(['action', 'status', 'local'])
            ->make(true);
    }
}
