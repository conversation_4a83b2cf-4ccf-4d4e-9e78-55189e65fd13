<?php

namespace App\Http\Requests;

use App\Job;
use App\Question;
use App\JobJobLocation;
use Illuminate\Support\Arr;

class StoreJobApplication extends CoreRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        $job = Job::where('id', $this->job_id)->first();

        $requiredColumns = $job->required_columns;
        $sectionVisibility = $job->section_visibility;

        $rules = [
            'first_name' => 'required',
            'last_name' => 'required',
            // 'email' => 'email|required',
            'email' => 'email',
            'phone' => 'numeric|required',
            'job_id' => 'required|exists:jobs,id',
        ];

        if ($requiredColumns['gender']) {
            $rules = Arr::add($rules, 'gender', 'required|in:male,female,others');
        }
        if ($requiredColumns['dob']) {
            $rules = Arr::add($rules, 'dob', 'required|date');
        }

        if (!is_null($sectionVisibility)) {
            foreach ($sectionVisibility as $key => $section) {
                if ($section === 'yes') {
                    if ($key === 'profile_image') {
                        $rules = Arr::add($rules, 'photo', 'required|mimes:jpeg,jpg,png');
                    }
                    if ($key === 'resume') {
                        $rules = Arr::add($rules, 'resume', 'required|mimes:jpeg,jpg,png,doc,docx,rtf,xls,xlsx,pdf');
                    }
                }
            }

        }

        $answers = $this->get('answer');
        if (isset($answers) && !empty($this->get('answer'))) {
            foreach ($this->get('answer') as $key => $value) {

                $answer = Question::where('id', $key)->first();
                if ($answer->required == 'yes') {
                    $rules["answer.{$key}"] = 'required';
                }
            }
        }

        return $rules;
    }

    public function messages()
    {
        return [
            'answer.*.required' => 'La respuesta a esta pregunta es obligatoria.',
            'dob.required' => 'La fecha de nacimiento es obligatoria.',
            'country.min' => 'Seleccione país.',
            'state.min' => 'Seleccione estado.',
            'city.required' => 'Seleccione ciudad.',
        ];
    }
}
