<?php $__env->startSection('page-title'); ?>
    <div class="row bg-title">
        <div class="col-lg-3 col-md-4 col-sm-4 col-xs-12">
            <h4 class="page-title"><i class="<?php echo e($pageIcon); ?>"></i> <?php echo e($pageTitle); ?></h4>
        </div>
        <div class="col-lg-9 col-sm-8 col-md-8 col-xs-12">
            <ol class="breadcrumb">
                <li><a href="<?php echo e(route('admin.dashboard')); ?>"><?php echo app('translator')->get('app.menu.home'); ?></a></li>
                <li class="active"><?php echo e($pageTitle); ?></li>
            </ol>
        </div>
    </div>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
    <div class="row">
        <div class="col-md-12">
            <div class="white-box">
                <h3 class="box-title"><?php echo app('translator')->get('menu.reports'); ?></h3>
                <p class="text-muted">Selecciona el tipo de reporte que deseas generar</p>
                
                <div class="row">
                    <!-- Reporte 1: Candidatos en Proceso -->
                    <div class="col-lg-4 col-md-6 col-sm-12 d-flex">
                        <div class="white-box bg-info p-3 m-2">
                            <div class="text-center d-flex flex-column h-100 justify-content-between">
                                <i class="icon-user text-white mb-2" style="font-size: 40px;"></i>
                                <h4 class="text-white">Candidatos en Proceso</h4>
                                <p class="text-white">Base de candidatos que están actualmente en proceso de selección</p>
                                <a href="<?php echo e(route('admin.reports.candidatos-en-proceso')); ?>" class="btn btn-outline-light btn-rounded"><span class="text-white">Ver Reporte</span></a>
                            </div>
                        </div>
                    </div>

                    <!-- Reporte 2: Histórico de Contratados -->
                    <div class="col-lg-4 col-md-6 col-sm-12 d-flex">
                        <div class="white-box bg-success p-3 m-2">
                            <div class="text-center d-flex flex-column h-100 justify-content-between">
                                <i class="icon-check text-white mb-2" style="font-size: 40px;"></i>
                                <h4 class="text-white">Histórico de Contratados</h4>
                                <p class="text-white">Base histórica de requerimientos cubiertos exitosamente</p>
                                <a href="<?php echo e(route('admin.reports.historico-contratados')); ?>" class="btn btn-outline-light btn-rounded">Ver Reporte</a>
                            </div>
                        </div>
                    </div>

                    <!-- Reporte 3: Requerimientos por Cubrir -->
                    <div class="col-lg-4 col-md-6 col-sm-12 d-flex">
                        <div class="white-box bg-warning p-3 m-2">
                            <div class="text-center d-flex flex-column h-100 justify-content-between">
                                <i class="icon-briefcase text-white" style="font-size: 40px;"></i>
                                <h4 class="text-white">Requerimientos por Cubrir</h4>
                                <p class="text-white">Trabajos activos que necesitan candidatos</p>
                                <a href="<?php echo e(route('admin.reports.requerimientos-por-cubrir')); ?>" class="btn btn-outline-light btn-rounded"><span class="text-white">Ver Reporte</span></a>
                            </div>
                        </div>
                    </div>

                    <!-- Reporte 4: Histórico de Cancelados -->
                    <div class="col-lg-4 col-md-6 col-sm-12 d-flex">
                        <div class="white-box bg-danger p-3 m-2">
                            <div class="text-center d-flex flex-column h-100 justify-content-between">
                                <i class="icon-close text-white" style="font-size: 40px;"></i>
                                <h4 class="text-white">Histórico de Cancelados</h4>
                                <p class="text-white">Base histórica de requerimientos cancelados</p>
                                <a href="<?php echo e(route('admin.reports.historico-cancelados')); ?>" class="btn btn-outline-light btn-rounded"><span class="text-white">Ver Reporte</span></a>
                            </div>
                        </div>
                    </div>

                    <!-- Reporte 5: Candidatos No Aptos -->
                    <div class="col-lg-4 col-md-6 col-sm-12 d-flex">
                        <div class="white-box bg-secondary p-3 m-2">
                            <div class="text-center d-flex flex-column h-100 justify-content-between">
                                <i class="icon-ban text-white" style="font-size: 40px;"></i>
                                <h4 class="text-white">Candidatos No Aptos</h4>
                                <p class="text-white">Base histórica de candidatos rechazados</p>
                                <a href="<?php echo e(route('admin.reports.candidatos-no-aptos')); ?>" class="btn btn-outline-light btn-rounded"><span class="text-white">Ver Reporte</span></a>
                            </div>
                        </div>
                    </div>

                    <!-- Reporte 6: Base General de Candidatos -->
                    <div class="col-lg-4 col-md-6 col-sm-12 d-flex">
                        <div class="white-box bg-primary p-3 m-2">
                            <div class="text-center d-flex flex-column h-100 justify-content-between">
                                <i class="icon-list text-white" style="font-size: 40px;"></i>
                                <h4 class="text-white">Base General de Candidatos</h4>
                                <p class="text-white">Reporte completo de todos los candidatos</p>
                                <a href="<?php echo e(route('admin.reports.base-general-candidatos')); ?>" class="btn btn-outline-light btn-rounded"><span class="text-white">Ver Reporte</span></a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH E:\laragon\www\recruit-seprocal\resources\views/admin/reports/index.blade.php ENDPATH**/ ?>