<!-- Main Sidebar Container -->
<aside class="main-sidebar sidebar-dark-primary">
  <!-- Brand Logo -->
  <a class="brand-link" href="{{ route('admin.dashboard') }}">
    <img alt="AdminLTE Logo" class="brand-image img-fluid" src="{{ $global->logo_url }}">
  </a>

  <!-- Sidebar -->
  <div class="sidebar">

    <!-- Sidebar Menu -->
    <nav class="mt-2">
      <ul class="nav nav-pills nav-sidebar flex-column" data-accordion="false" data-widget="treeview" id="sidebarnav" role="menu">
        <!-- Add icons to the links using the .nav-icon class
           with font-awesome or any other icon font library -->
        <li class="nav-item">
          <a class="nav-link {{ request()->is('admin/dashboard*') ? 'active' : '' }}" href="{{ route('admin.dashboard') }}">
            <i class="nav-icon icon-speedometer"></i>
            <p>
              @lang('menu.dashboard')
            </p>
          </a>
        </li>

        @if (in_array('view_category', $userPermissions))
          <li class="nav-item">
            <a class="nav-link {{ request()->is('admin/job-categories*') ? 'active' : '' }}" href="{{ route('admin.job-categories.index') }}">
              <i class="nav-icon icon-grid"></i>
              <p>
                @lang('menu.jobCategories')
              </p>
            </a>
          </li>
        @endif
        @if (in_array('view_job_description', $userPermissions))
          <li class="nav-item">
            <a class="nav-link {{ request()->is('admin/job_description*') ? 'active' : '' }}" href="{{ route('admin.job_descriptions.index') }}">
              <i class="nav-icon icon-briefcase"></i>
              <p>
                @lang('menu.jobDescriptions')
              </p>
            </a>
          </li>
        @endif
        @if (in_array('view_skills', $userPermissions))
          <li class="nav-item">
            <a class="nav-link {{ request()->is('admin/skills*') ? 'active' : '' }}" href="{{ route('admin.skills.index') }}">
              <i class="nav-icon icon-grid"></i>
              <p>
                @lang('menu.skills')
              </p>
            </a>
          </li>
        @endif

        @if (in_array('view_company', $userPermissions))
          <li class="nav-item">
            <a class="nav-link {{ request()->is('admin/company*') ? 'active' : '' }}" href="{{ route('admin.company.index') }}">
              <i class="nav-icon icon-film"></i>
              <p>
                @lang('menu.companies')
              </p>
            </a>
          </li>
        @endif

        @if (in_array('view_cost_center', $userPermissions))
          <li class="nav-item">
            <a class="nav-link {{ request()->is('admin/cost_centers*') ? 'active' : '' }}" href="{{ route('admin.cost_centers.index') }}">
              <i class="nav-icon icon-pie-chart"></i>
              <p>@lang('menu.cost_centers')</p>
            </a>
          </li>
        @endif
        <li class="nav-item">
          <a class="nav-link {{ request()->is('admin/clinic*') ? 'active' : '' }}" href="{{ route('admin.clinics.index') }}">
            <i class="nav-icon icon-chemistry"></i>
            <p>
              @lang('menu.clinics')
            </p>
          </a>
        </li>
        @if (in_array('view_locations', $userPermissions))
          <li class="nav-item">
            <a class="nav-link {{ request()->is('admin/locations*') ? 'active' : '' }}" href="{{ route('admin.locations.index') }}">
              <i class="nav-icon icon-location-pin"></i>
              <p>
                @lang('menu.locations')
              </p>
            </a>
          </li>
        @endif

        @if (in_array('view_jobs', $userPermissions))
          <li class="nav-item">
            <a class="nav-link {{ request()->is('admin/jobs*') ? 'active' : '' }}" href="{{ route('admin.jobs.index') }}">
              <i class="nav-icon icon-badge"></i>
              <p>
                @lang('menu.jobs')
              </p>
            </a>
          </li>
        @endif

        @if (in_array('view_job_applications', $userPermissions))
          <li class="nav-item">
            <a class="nav-link {{ request()->is('admin/job-applications*') ? 'active' : '' }}" href="{{ route('admin.job-applications.index') }}">
              <i class="nav-icon icon-user"></i>
              <p>
                @lang('menu.jobApplications')
              </p>
            </a>
          </li>
          <li class="nav-item">
            <a class="nav-link {{ request()->is('admin/applications-archive*') ? 'active' : '' }}" href="{{ route('admin.applications-archive.index') }}">
              <i class="nav-icon icon-drawer"></i>
              <p>
                @lang('menu.candidateDatabase')
              </p>
            </a>
          </li>
          <li class="nav-item">
            <a class="nav-link {{ request()->is('admin/job-onboard*') ? 'active' : '' }}" href="{{ route('admin.job-onboard.index') }}">
              <i class="nav-icon icon-user"></i>
              <p>
                @lang('menu.jobOnboard')
              </p>
            </a>
          </li>
        @endif

        @if (in_array('view_schedule', $userPermissions))
          <li class="nav-item">
            <a class="nav-link {{ request()->is('admin/interview-schedule*') ? 'active' : '' }}" href="{{ route('admin.interview-schedule.index') }}">
              <i class="nav-icon icon-calendar"></i>
              <p>
                @lang('menu.interviewSchedule')
              </p>
            </a>
          </li>
        @endif

        @if (in_array('view_empleopolis', $userPermissions))
          <li class="nav-item">
            <a class="nav-link {{ request()->is('admin/empleopolis*') ? 'active' : '' }}" href="{{ route('admin.empleopolis.index') }}">
              <i class="nav-icon icon-doc"></i>
              <p>
                Filtro interno
              </p>
            </a>
          </li>
        @endif

        @if (in_array('view_signus', $userPermissions))
          <li class="nav-item">
            <a class="nav-link {{ request()->is('admin/signus*') ? 'active' : '' }}" href="{{ route('admin.signus.index') }}">
              <i class="nav-icon icon-folder-alt"></i>
              <p>
                Filtro del cliente
              </p>
            </a>
          </li>
        @endif

        @if (in_array('view_medical_exams', $userPermissions))
          <li class="nav-item">
            <a class="nav-link {{ request()->is('admin/medical-exam*') ? 'active' : '' }}" href="{{ route('admin.medical-exam.index') }}">
              <i class="nav-icon icon-briefcase"></i>
              <p>
                Examen Médico
              </p>
            </a>
          </li>
        @endif

        @if (in_array('view_team', $userPermissions))
          <li class="nav-item">
            <a class="nav-link {{ request()->is('admin/team*') ? 'active' : '' }}" href="{{ route('admin.team.index') }}">
              <i class="nav-icon icon-people"></i>
              <p>
                @lang('menu.team')
              </p>
            </a>
          </li>
        @endif

        @if ($user->roles->count() > 0)
          <li class="nav-item">
            <a class="nav-link {{ request()->is('admin/todo-items*') ? 'active' : '' }}" href="{{ route('admin.todo-items.index') }}">
              <i class="nav-icon icon-notebook"></i>
              <p>
                @lang('menu.todoList')
              </p>
            </a>
          </li>
        @endif

        @if ($user->roles->count() > 0)
          <li class="nav-item">
            <a class="nav-link {{ request()->is('admin/job_alert*') ? 'active' : '' }}" href="{{ route('admin.job_alert.index') }}">
              <i class="nav-icon icon-bell"></i>
              <p>
                @lang('menu.jobAlert')
              </p>
            </a>
          </li>
        @endif

        @if (in_array('view_reports', $userPermissions))
        <li class="nav-item has-treeview @if (\Request()->is('admin/reports*') || \Request()->is('admin/report*')) active menu-open @endif">
          <a class="nav-link" href="#">
            <i aria-hidden="true" class="fa fa-bar-chart"></i>
            <p>
              @lang('app.reports')
              <i class="right fa fa-angle-left"></i>
            </p>
          </a>
          <ul class="nav nav-treeview">
            <li class="nav-item">
              <a class="nav-link {{ request()->is('admin/reports/candidatos-en-proceso*') ? 'active' : '' }}" href="{{ route('admin.reports.candidatos-en-proceso') }}">
                <i class="fa fa-users nav-icon"></i>
                <p>Candidatos en Proceso</p>
              </a>
            </li>
            <li class="nav-item">
              <a class="nav-link {{ request()->is('admin/reports/historico-contratados*') ? 'active' : '' }}" href="{{ route('admin.reports.historico-contratados') }}">
                <i class="fa fa-check nav-icon"></i>
                <p>Histórico de Contratados</p>
              </a>
            </li>
            <li class="nav-item">
              <a class="nav-link {{ request()->is('admin/reports/requerimientos-por-cubrir*') ? 'active' : '' }}" href="{{ route('admin.reports.requerimientos-por-cubrir') }}">
                <i class="fa fa-briefcase nav-icon"></i>
                <p>Requerimientos por Cubrir</p>
              </a>
            </li>
            <li class="nav-item">
              <a class="nav-link {{ request()->is('admin/reports/historico-cancelados*') ? 'active' : '' }}" href="{{ route('admin.reports.historico-cancelados') }}">
                <i class="fa fa-times nav-icon"></i>
                <p>Histórico de Cancelados</p>
              </a>
            </li>
            <li class="nav-item">
              <a class="nav-link {{ request()->is('admin/reports/candidatos-no-aptos*') ? 'active' : '' }}" href="{{ route('admin.reports.candidatos-no-aptos') }}">
                <i class="fa fa-ban nav-icon"></i>
                <p>Candidatos No Aptos</p>
              </a>
            </li>
            <li class="nav-item">
              <a class="nav-link {{ request()->is('admin/reports/base-general-candidatos*') ? 'active' : '' }}" href="{{ route('admin.reports.base-general-candidatos') }}">
                <i class="fa fa-list nav-icon"></i>
                <p>Base General de Candidatos</p>
              </a>
            </li>
            <li class="nav-item">
              <a class="nav-link {{ request()->is('admin/report*') ? 'active' : '' }}" href="{{ route('admin.report.index') }}">
                <i class="fa fa-chart-line nav-icon"></i>
                <p>Reportes del Sistema</p>
              </a>
            </li>
          </ul>
        </li>
        @endif
        @if (in_array('view_schedule', $userPermissions))
          @if (isset($zoom_setting->enable_zoom) && $zoom_setting->enable_zoom == 1)
            <li class="nav-item has-treeview">
              <a class="nav-link" href="{{ route('admin.zoom-meeting.table-view') }}">
                <i class="fa fa-video-camera"></i>
                <p> @lang('menu.zoomMeeting')</p>
              </a>
            </li>
          @endif
        @endif
        <li class="nav-item has-treeview @if (\Request()->is('admin/settings/*') || \Request()->is('admin/profile')) active menu-open @endif">
          <a class="nav-link" href="#">
            <i class="nav-icon icon-settings"></i>
            <p>
              @lang('menu.settings')
              <i class="right fa fa-angle-left"></i>
            </p>
          </a>
          <ul class="nav nav-treeview">
            <li class="nav-item">
              <a class="nav-link {{ request()->is('admin/profile*') ? 'active' : '' }}" href="{{ route('admin.profile.index') }}">
                <i class="fa fa-circle-o nav-icon"></i>
                <p> @lang('menu.myProfile')</p>
              </a>
            </li>
            @if (in_array('manage_settings', $userPermissions))
              <li class="nav-item">
                <a class="nav-link {{ request()->is('admin/settings/settings') ? 'active' : '' }}" href="{{ route('admin.settings.index') }}">
                  <i class="fa fa-circle-o nav-icon"></i>
                  <p>@lang('menu.businessSettings')</p>
                </a>
              </li>
              <li class="nav-item">
                <a class="nav-link {{ request()->is('admin/settings/application-setting') ? 'active' : '' }}"
                   href="{{ route('admin.application-setting.index') }}">
                  <i class="fa fa-circle-o nav-icon"></i>
                  <p>@lang('menu.applicationFormSettings')</p>
                </a>
              </li>
              <li class="nav-item">
                <a class="nav-link {{ request()->is('admin/settings/currency-settings') ? 'active' : '' }}"
                   href="{{ route('admin.currency-settings.index') }}">
                  <i class="fa fa-circle-o nav-icon"></i>
                  <p>@lang('menu.currencySetting')</p>
                </a>
              </li>
              <li class="nav-item">
                <a class="nav-link {{ request()->is('admin/settings/role-permission') ? 'active' : '' }}" href="{{ route('admin.role-permission.index') }}">
                  <i class="fa fa-circle-o nav-icon"></i>
                  <p>@lang('menu.rolesPermission')</p>
                </a>
              </li>
              <li class="nav-item">
                <a class="nav-link {{ request()->is('admin/settings/language-settings') ? 'active' : '' }}"
                   href="{{ route('admin.language-settings.index') }}">
                  <i class="fa fa-circle-o nav-icon"></i>
                  <p>@lang('app.language') @lang('menu.settings')</p>
                </a>
              </li>
              <li class="nav-item">
                <a class="nav-link {{ request()->is('admin/settings/footer-settings') ? 'active' : '' }}" href="{{ route('admin.footer-settings.index') }}">
                  <i class="fa fa-circle-o nav-icon"></i>
                  <p>@lang('menu.footerSettings')</p>
                </a>
              </li>
              <li class="nav-item">
                <a class="nav-link {{ request()->is('admin/settings/theme-settings') ? 'active' : '' }}" href="{{ route('admin.theme-settings.index') }}">
                  <i class="fa fa-circle-o nav-icon"></i>
                  <p>@lang('menu.themeSettings')</p>
                </a>
              </li>
              <li class="nav-item">
                <a class="nav-link {{ request()->is('admin/settings/smtp-settings') ? 'active' : '' }}" href="{{ route('admin.smtp-settings.index') }}">
                  <i class="fa fa-circle-o nav-icon"></i>
                  <p>@lang('menu.mailSetting')</p>
                </a>
              </li>
              <li class="nav-item">
                <a class="nav-link {{ request()->is('admin/settings/sms-settings') ? 'active' : '' }}" href="{{ route('admin.sms-settings.index') }}">
                  <i class="fa fa-circle-o nav-icon"></i>
                  <p>@lang('menu.smsSettings')</p>
                </a>
              </li>
              <li class="nav-item">
                <a class="nav-link" href="{{ route('admin.storage-settings.index') }}">
                  <i class="fa fa-circle-o nav-icon"></i>
                  <p>@lang('menu.storageSetting')</p>
                </a>
              </li>
              <li class="nav-item">
                <a class="nav-link {{ request()->is('admin/settings/sms-settings') ? 'active' : '' }}" href="{{ route('admin.security-setting.index') }}">
                  <i class="fa fa-circle-o nav-icon"></i>
                  <p>@lang('menu.securitySettings')</p>
                </a>
              </li>

              <li class="nav-item">
                <a class="nav-link" href="{{ route('admin.linkedin-settings.index') }}">
                  <i class="fa fa-circle-o nav-icon"></i>
                  <p>@lang('menu.linkedInSettings')</p>
                </a>
              </li>
              @if ($global->system_update == 1)
                <li class="nav-item">
                  <a class="nav-link {{ request()->is('admin/settings/update-application') ? 'active' : '' }}"
                     href="{{ route('admin.update-application.index') }}">
                    <i class="fa fa-circle-o nav-icon"></i>
                    <p>@lang('menu.updateApplication')</p>
                  </a>
                </li>
              @endif
              <li class="nav-item">
                <a class="nav-link" href="{{ route('admin.zoom-setting.index') }}">
                  <i class="fa fa-circle-o nav-icon"></i>

                  <p> @lang('menu.zoomSetting')</p>
                </a>
              </li>
              <li class="nav-item">
                <a class="nav-link" href="https://froiden.freshdesk.com/support/solutions/" target="_blank">
                  <i class="fa fa-circle-o nav-icon"></i>
                  <p>@lang('menu.help')</p>
                </a>
              </li>

            @endif


          </ul>
        </li>

        <li class="nav-header">MISCELLANEOUS</li>
        <li class="nav-item">
          <a class="nav-link" href="{{ url('/') }}" target="_blank">
            <i class="nav-icon fa fa-external-link"></i>
            <p>Front Website</p>
          </a>
        </li>

      </ul>
    </nav>
    <!-- /.sidebar-menu -->
  </div>
  <!-- /.sidebar -->
</aside>
