<?php

namespace App\Http\Controllers\Admin;

use App\Clinic;
use App\Helper\Reply;
use Yajra\DataTables\DataTables;
use App\Http\Requests\Admin\Clinic\StoreRequest;

class AdminClinicController extends AdminBaseController
{
    public function __construct()
    {
        parent::__construct();
        $this->pageTitle = __('app.clinic');
        $this->pageIcon = 'icon-user';
    }

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        return view('admin.clinics.index', $this->data);
    }

    public function create()
    {
        return view('admin.clinics.create', $this->data);
    }


    public function edit($id)
    {
        $this->clinic = Clinic::find($id);
        return view('admin.clinics.edit', $this->data);
    }

    public function store(StoreRequest $request)
    {
        $data = $request->all();
        Clinic::create($data);
        return Reply::redirect(route('admin.clinics.index'), __('menu.clinic') . ' ' . __('messages.createdSuccessfully'));
    }

    public function update(StoreRequest $request, $id)
    {
        abort_if(!$this->user->cans('edit_clinic'), 403);

        $data = $request->all();
        $costCenter = Clinic::findOrFail($id);
        $costCenter->update($data);

        $costCenterData = Clinic::all();
        return Reply::redirect(route('admin.clinics.index'), __('menu.clinic') . ' ' . __('messages.clinicUpdated'));
        // return Reply::successWithData(__('messages.clinicUpdated'),['data' => $costCenterData]);
    }

    public function destroy($id)
    {
        Clinic::destroy($id);

        $costCenterData = Clinic::all();
        return Reply::successWithData(__('messages.recordDeleted'), ['data' => $costCenterData]);
    }

    public function show($id)
    {
        //
    }

    public function data()
    {
        $clinic = Clinic::all();

        return DataTables::of($clinic)
            ->addColumn('action', function ($row) {
                $action = '';
                $action .= '<a href="' . route('admin.clinics.edit', [$row->id]) . '" class="btn btn-primary btn-circle"
                  data-toggle="tooltip" onclick="this.blur()" data-original-title="'.__('app.edit').'"><i class="fa fa-pencil" aria-hidden="true"></i></a>';
                $action .= ' <a href="#" class="btn btn-danger btn-circle sa-params"
                  data-toggle="tooltip" onclick="this.blur()" data-row-id="' . $row->id . '" data-original-title="'.__('app.delete').'"><i class="fa fa-times" aria-hidden="true"></i></a>';

                return $action;
            })
            ->editColumn('name', function ($row) {
                return ucfirst($row->name);
            })
            ->addIndexColumn()
            ->make(true);
    }
}
