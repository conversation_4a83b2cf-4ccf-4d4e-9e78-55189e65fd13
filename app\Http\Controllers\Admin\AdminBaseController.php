<?php

namespace App\Http\Controllers\Admin;

use App\User;
use App\Currency;
use Carbon\Carbon;
use App\StickyNote;
use App\ZoomSetting;
use App\ThemeSetting;
use App\CompanySetting;
use App\LanguageSetting;
use App\LinkedInSetting;
use App\ApplicationSetting;
use Illuminate\Support\Facades\App;
use App\Http\Controllers\Controller;
use App\Traits\FileSystemSettingTrait;

class AdminBaseController extends Controller
{
    use FileSystemSettingTrait;
    /**
     * @var array
     */
    public $data = [];

    /**
     * @param $name
     * @param $value
     */
    public function __set($name, $value)
    {
        $this->data[$name] = $value;
    }

    /**
     * @param $name
     * @return mixed
     */
    public function __get($name)
    {
        return $this->data[$name];
    }

    /**
     * @param $name
     * @return bool
     */
    public function __isset($name)
    {
        return isset($this->data[ $name ]);
    }

    /**
     * UserBaseController constructor.
     */
    public function __construct()
    {
        // Inject currently logged in user object into every view of user dashboard
        parent::__construct();

        $this->global = CompanySetting::first();

        $this->companyName = $this->global->company_name;

        $this->adminTheme = ThemeSetting::first();

        $this->applicationSetting = ApplicationSetting::first();

        $this->languageSettings = LanguageSetting::where('status', 'enabled')->orderBy('language_name')->get();
        $this->currencySettings = Currency::all();

        $this->zoom_setting = ZoomSetting::first();

        App::setLocale($this->global->locale);
        Carbon::setLocale($this->global->locale);
        setlocale(LC_TIME, $this->global->locale.'_'.strtoupper($this->global->locale));

        $this->middleware(function ($request, $next) {
            $this->user = auth()->user();
            $this->todoItems = $this->user->todoItems()->groupBy('status', 'position')->get();
            $this->linkedinGlobal = LinkedInSetting::first();
            $this->stickyNotes = StickyNote::where('user_id', $this->user->id)
                ->orderBy('updated_at', 'desc')
                ->get();
            $this->setFileSystemConfigs();
            $this->getPermissions = User::with('roles.permissions.permission')->find($this->user->id);
            $userPermissions = [];
            foreach ($this->getPermissions->roles[0]->permissions as $key => $value) {
                $userPermissions[] = $value->permission->name;
            }
            $this->userPermissions = $userPermissions;

            view()->share('languages', $this->languageSettings);

            view()->share('global', $this->global);

            return $next($request);
        });
    }

    public function generateTodoView()
    {
        $pendingTodos = $this->user->todoItems()->status('pending')->orderBy('position', 'DESC')->limit(5)->get();
        $completedTodos = $this->user->todoItems()->status('completed')->orderBy('position', 'DESC')->limit(5)->get();

        $view = view('sections.todo_items_list', compact('pendingTodos', 'completedTodos'))->render();

        return $view;
    }
}
