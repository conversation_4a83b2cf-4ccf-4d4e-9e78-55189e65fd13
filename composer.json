{"name": "laravel/laravel", "description": "The Laravel Framework.", "keywords": ["framework", "laravel"], "license": "MIT", "type": "project", "require": {"php": "^8.0", "barryvdh/laravel-dompdf": "^3.1", "barryvdh/laravel-translation-manager": "^0.6.3", "cviebrock/eloquent-sluggable": "9.0.*", "doctrine/dbal": "^3.0", "fideloper/proxy": "^4.4", "froiden/envato": "^1.0", "froiden/laravel-installer": "^1.6", "froiden/laravel-rest-api": "^9.0", "guzzlehttp/guzzle": "^7.2", "intervention/image": "^2.5", "laravel/framework": "^9.19", "laravel/helpers": "^1.4", "laravel/socialite": "^5.1", "laravel/tinker": "^2.7", "laravel/ui": "^3.4", "laravel/vonage-notification-channel": "^3.0", "laravelcollective/html": "^6.2", "league/flysystem": "^3.0", "league/flysystem-aws-s3-v3": "^3.0", "maatwebsite/excel": "~3.1", "macellan/laravel-zip": "^1.0", "macsidigital/laravel-zoom": "^8.0", "mitchbred/entrust": "^2.1", "nexmo/laravel": "dev-l9-compatibility", "nwidart/laravel-modules": "^8.2", "php-http/guzzle6-adapter": "^2.0", "simplesoftwareio/simple-qrcode": "~4", "swiftmailer/swiftmailer": "^6.3", "symfony/mailer": "^6.0", "tymon/jwt-auth": "dev-develop", "yajra/laravel-datatables-buttons": "^4.10", "yajra/laravel-datatables-html": "^4.36", "yajra/laravel-datatables-oracle": "^9.14"}, "require-dev": {"barryvdh/laravel-debugbar": "^3.5", "barryvdh/laravel-ide-helper": "*", "fakerphp/faker": "^1.9.1", "laravel/pint": "^1.0", "mockery/mockery": "^1.4.4", "nunomaduro/collision": "^6.1", "phpunit/phpunit": "^9.5.10", "spatie/laravel-ignition": "^1.0"}, "repositories": [{"type": "vcs", "url": "https://github.com/Froiden/guzzle6-adapter"}, {"type": "vcs", "url": "https://github.com/laravel-shift/nexmo********.git"}], "autoload": {"classmap": ["database/seeds", "database/factories"], "files": ["start.php"], "psr-4": {"App\\": "app/", "Modules\\": "Mo<PERSON>les/"}}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "extra": {"laravel": {"dont-discover": []}}, "scripts": {"post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate"], "post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover"]}, "config": {"preferred-install": "dist", "sort-packages": true, "optimize-autoloader": true}, "minimum-stability": "dev", "prefer-stable": true}