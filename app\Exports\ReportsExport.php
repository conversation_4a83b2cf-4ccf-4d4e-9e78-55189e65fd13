<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\WithTitle;
use Illuminate\Support\Collection;

class ReportsExport implements FromCollection, WithHeadings, WithMapping, WithTitle
{
    protected $data;
    protected $headings;
    protected $title;
    protected $type;

    public function __construct($data, $headings, $title, $type = 'candidatos')
    {
        $this->data = $data;
        $this->headings = $headings;
        $this->title = $title;
        $this->type = $type;
    }

    public function collection()
    {
        return collect($this->data);
    }

    public function headings(): array
    {
        return $this->headings;
    }

    public function map($row): array
    {
        switch ($this->type) {
            case 'candidatos':
                return [
                    $row->id ?? '',
                    $this->formatDatosPersonales($row),
                    $this->formatUnidadCentroCosto($row),
                    $this->formatPuestoCategoria($row),
                    $row->titulo_etapa ?? $row->etapa_actual ?? '',
                    $this->formatFecha($row->updated_at ?? $row->created_at),
                    $this->formatCondicionesContratacion($row),
                    $row->responsable_proceso ?? ''
                ];
            
            case 'contratados':
                return [
                    $row->id ?? '',
                    $this->formatDatosPersonales($row),
                    $this->formatUnidadCentroCosto($row),
                    $this->formatPuestoCategoria($row),
                    $this->formatCondicionesContratacion($row),
                    $this->formatFecha($row->fecha_subida),
                    $row->responsable_proceso ?? ''
                ];
            
            case 'requerimientos':
                return [
                    $row->id ?? '',
                    $this->calcularSemana($row->start_date),
                    $this->formatFecha($row->start_date),
                    $this->formatUnidadCentroCosto($row),
                    $this->formatPuestoCategoria($row),
                    $row->total_positions ?? ''
                ];
            
            case 'cancelados':
                return [
                    $row->id ?? '',
                    $this->calcularSemana($row->updated_at),
                    $this->formatFecha($row->updated_at),
                    $row->canceled_reason ?? 'No especificado',
                    $this->formatUnidadCentroCosto($row),
                    $this->formatPuestoCategoria($row),
                    $row->total_positions ?? ''
                ];
            
            case 'no_aptos':
                return [
                    $row->id ?? '',
                    $this->formatDatosPersonales($row),
                    $this->formatUnidadCentroCosto($row),
                    $this->formatPuestoCategoria($row),
                    $this->obtenerMotivoRechazo($row->id),
                    $this->formatFecha($row->updated_at)
                ];
            
            default:
                return [];
        }
    }

    public function title(): string
    {
        return $this->title;
    }

    private function formatDatosPersonales($row)
    {
        $datos = "Nombre: " . ($row->full_name ?? '');
        $datos .= " | Email: " . ($row->email ?? '');
        $datos .= " | Teléfono: " . ($row->phone ?? '');
        $datos .= " | Género: " . ($row->gender ?? '');
        if ($row->dob) {
            $datos .= " | Fecha Nac: " . \Carbon\Carbon::parse($row->dob)->format('d/m/Y');
        }
        return $datos;
    }

    private function formatUnidadCentroCosto($row)
    {
        $unidad = "Unidad: " . ($row->unidad_minera ?? 'No especificada');
        $centro = " | Centro Costo: " . ($row->centro_costo ?? 'No especificado');
        if ($row->codigo_centro_costo) {
            $centro .= " (" . $row->codigo_centro_costo . ")";
        }
        return $unidad . $centro;
    }

    private function formatPuestoCategoria($row)
    {
        $puesto_completo = $row->puesto ?? '';
        if ($row->descripcion_puesto) {
            $puesto_completo .= " - " . $row->descripcion_puesto;
        }
        
        $categoria = $this->determinarCategoriaPuesto($row->categoria_puesto ?? '');
        return "Puesto: " . $puesto_completo . " | Categoría: " . $categoria;
    }

    private function formatCondicionesContratacion($row)
    {
        $salario = 'No especificado';
        if ($row->salary_offered ?? false) {
            $salario = "S/ " . $row->salary_offered;
        } elseif ($row->starting_salary ?? false) {
            $salario = "S/ " . $row->starting_salary;
            if ($row->maximum_salary && $row->maximum_salary != $row->starting_salary) {
                $salario .= " - S/ " . $row->maximum_salary;
            }
        }
        
        return "Tipo: " . ($row->pay_type ?? 'No especificado') . " | Salario: " . $salario;
    }

    private function formatFecha($fecha)
    {
        if (!$fecha) return 'No especificada';
        return \Carbon\Carbon::parse($fecha)->format('d/m/Y');
    }

    private function calcularSemana($fecha)
    {
        if (!$fecha) return 'No especificada';
        return 'Semana ' . \Carbon\Carbon::parse($fecha)->weekOfYear;
    }

    private function determinarCategoriaPuesto($categoria)
    {
        if (!$categoria) return 'No especificada';
        
        $categorias_obreros = ['operario', 'obrero', 'técnico', 'operador', 'ayudante'];
        $categoria_lower = strtolower($categoria);
        
        foreach ($categorias_obreros as $cat_obrero) {
            if (strpos($categoria_lower, $cat_obrero) !== false) {
                return 'OBRERO';
            }
        }
        
        return 'EMPLEADO';
    }

    private function obtenerMotivoRechazo($jobApplicationId)
    {
        // Simplificado para exportación
        return 'Ver sistema para detalles';
    }
}
