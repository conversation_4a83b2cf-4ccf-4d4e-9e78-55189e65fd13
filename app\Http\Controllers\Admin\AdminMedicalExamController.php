<?php

namespace App\Http\Controllers\Admin;

use App;
use App\Clinic;
use App\Document;
use Carbon\Carbon;
use App\MedicalExam;
use App\Helper\Files;
use App\JobApplication;
use App\ApplicationStatus;
use Illuminate\Http\Request;
use Froiden\Envato\Helpers\Reply;
use Illuminate\Support\Facades\DB;
use Yajra\DataTables\Facades\DataTables;
use App\Http\Requests\MedicalExam\StoreRequest;
use App\Http\Requests\MedicalExam\UpdateRequest;

class AdminMedicalExamController extends AdminBaseController
{
    public function __construct()
    {
        parent::__construct();
        $this->pageTitle = 'Examen Médico';
        $this->pageIcon = 'icon-doc';
    }

    public function index()
    {
        abort_if(!$this->user->cans('view_medical_exams'), 403);
        $this->candidates = JobApplication::all();
        $this->clinics = Clinic::all();
        return view('admin.medical-exam.index', $this->data);
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create(Request $request)
    {
        abort_if(!$this->user->cans('add_medical_exams'), 403);
        $this->candidates = JobApplication::all();
        $this->clinics = Clinic::all();
        $this->currentCandidateId = $request->id ?? null;
        return view('admin.medical-exam.create', $this->data)->render();
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(StoreRequest $request)
    {
        abort_if(!$this->user->cans('add_medical_exams'), 403);
        $jobApplication = JobApplication::find($request->candidate);
        $medicalExam = MedicalExam::create([
            'job_application_id'    => $request->candidate,
            'medical_exam_date'     => $request->medical_exam_date,
            'results_date'          => $request->results_date,
            'status'                => $request->status,
            'clinic_id'             => $request->clinic_id,
            'comments'              => $request->comments,
        ]);

        $message = __('messages.createdSuccessfully');
        if ($jobApplication->status->status != 'examen médico') {
            $min_column_prority = JobApplication::where('status_id', ApplicationStatus::where('status', 'examen médico')->pluck('id'))->min('column_priority');
            $jobApplication->column_priority = $min_column_prority - 1;
            $jobApplication->status_id = ApplicationStatus::where('status', 'examen médico')->first()->id;
            $jobApplication->save();
        }
        $names = $request->name;
        $files = $request->file;
        if ($request->has('file')) {
            foreach ($names as $key => $name) {
                // Files store in directory
                $hashname = Files::uploadLocalOrS3($files[$key], 'medical-exam/' . $jobApplication->id, null, null, false);
                $medicalExam->documents()->create([
                    'name' => $name,
                    'hashname' => $hashname,
                ]);
            }
        }

        return Reply::success(__('menu.medical_exam') . ' ' . $message, json_encode($medicalExam));
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        abort_if(!$this->user->cans('view_medical_exams'), 403);
        $this->medical_exam = MedicalExam::with(['jobApplication'])->find($id);
        return view('admin.medical-exam.show', $this->data)->render();
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        abort_if(!$this->user->cans('edit_medical_exams'), 403);
        $this->candidates = JobApplication::all();
        $this->clinics = Clinic::all();
        $this->medical_exam = MedicalExam::with(['jobApplication'])->find($id);
        return view('admin.medical-exam.edit', $this->data)->render();
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(UpdateRequest $request, $id)
    {
        abort_if(!$this->user->cans('add_medical_exams'), 403);
        $medical_exam = MedicalExam::findOrFail($id);
        $jobApplication = JobApplication::find($request->candidate);

        $medical_exam->job_application_id = $request->candidate;
        $medical_exam->medical_exam_date = $request->medical_exam_date;
        $medical_exam->clinic_id = $request->clinic_id;
        $medical_exam->status = $request->status;
        $medical_exam->comments = $request->comments;
        $medical_exam->save();
        if ($jobApplication->status->status != 'examen médico') {
            $min_column_prority = JobApplication::where('status_id', ApplicationStatus::where('status', 'examen médico')->pluck('id'))->min('column_priority');
            $jobApplication->column_priority = $min_column_prority - 1;
            $jobApplication->status_id = ApplicationStatus::where('status', 'examen médico')->first()->id;
            $jobApplication->save();
        }
        $names = $request->name;
        $files = $request->file;
        if ($request->documents) {
            foreach ($medical_exam->documents as $document) {
                if (!in_array($document->id, $request->documents)) {
                    $document->delete();
                }
                foreach ($request->documents as $document) {
                    $doc = Document::find((int)$document);
                    $medical_exam->documents()->save($doc);
                }
            }
        } else {
            $medical_exam->documents()->delete();
        }
        if ($request->has('file')) {
            foreach ($names as $key => $name) {
                // Files store in directory
                $hashname = Files::uploadLocalOrS3($files[$key], 'medical-exam/' . $jobApplication->id, null, null, false);
                $medical_exam->documents()->create([
                    'name' => $name,
                    'hashname' => $hashname,
                ]);
            }
        }

        return Reply::success(__('menu.medical_exam') . ' ' . __('messages.updatedSuccessfully'), json_encode($medical_exam));
        // return Reply::redirect(route('admin.medical-exam.index'), __('menu.medical_exam') . ' ' . __('messages.updatedSuccessfully'));
    }
    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        abort_if(!$this->user->cans('delete_medical_exams'), 403);

        MedicalExam::destroy($id);
        return Reply::success(__('messages.recordDeleted'));
    }

    /**
     * @throws \Exception
     */
    public function data(Request $request)
    {
        abort_if(!$this->user->cans('view_medical_exams'), 403);
        $query = MedicalExam::with('clinic')->select('medical_exams.id', 'job_applications.full_name as full_name', 'clinics.name as clinic_name', 'medical_exams.status', 'medical_exams.medical_exam_date')
            ->leftjoin('job_applications', 'job_applications.id', 'medical_exams.job_application_id')
            ->leftjoin('clinics', 'clinics.id', 'medical_exams.clinic_id');

        // Filter by status
        if ($request->status != 'all' && $request->status != '') {
            $query = $query->where('medical_exams.status', $request->status);
        }

        // Filter By candidate
        if ($request->applicationID != 'all' && $request->applicationID != '') {
            $query = $query->where('job_applications.id', $request->applicationID);
        }

        // Filter by StartDate
        if ($request->startDate !== null && $request->startDate != 'null') {
            $query = $query->where(DB::raw('DATE(medical_exam.`medical_exam_date`)'), '>=', "{$request->startDate}");
        }

        // Filter by EndDate
        if ($request->endDate !== null && $request->endDate != 'null') {
            $query = $query->where(DB::raw('DATE(medical_exam.`medical_exam_date`)'), '<=', "{$request->endDate}");
        }

        return DataTables::of($query)
            ->addColumn('action', function ($row) {
                $action = '';
                if ($this->user->cans('view_medical_exams')) {
                    $action .= '<a href="#" data-row-id="' . $row->id . '" class="btn btn-info btn-circle view-data"
                      data-toggle="tooltip" onclick="this.blur()" data-original-title="' . __('app.view') . '"><i class="fa fa-search" aria-hidden="true"></i></a>';
                }
                if ($this->user->cans('edit_medical_exams')) {
                    $action .= '<a href="#" style="margin-left:4px" data-row-id="' . $row->id . '" class="btn btn-primary btn-circle edit-data"
                      data-toggle="tooltip" onclick="this.blur()" data-original-title="' . __('app.edit') . '"><i class="fa fa-pencil" aria-hidden="true"></i></a>';
                }
                return $action;
            })
            ->addColumn('checkbox', function ($row) {
                return '
                    <div class="checkbox form-check">
                        <input id="' . $row->id . '" type="checkbox" name="id[]" class="form-check-input" value="' . $row->id . '" >
                        <label for="' . $row->id . '"></label>
                    </div>
                ';
            })
            ->editColumn('clinic', function ($row) {
                return ucwords($row->clinic_name);
            })
            ->editColumn('job_applications.full_name', function ($row) {
                return ucwords($row->full_name);
            })
            ->editColumn('medical_exam_date', function ($row) {
                Carbon::setlocale(config('app.locale'));
                return is_null($row->medical_exam_date) ? null : Carbon::parse($row->medical_exam_date)->translatedFormat('d \de F \de Y');
            })
            ->editColumn('status', function ($row) {
                if ($row->status == 'pending') {
                    return '<label class="badge bg-primary">' . __('app.pending') . '</label>';
                }
                if ($row->status == 'suitable') {
                    return '<label class="badge bg-success">' . __('app.suitable') . '</label>';
                }
                if ($row->status == 'suitable_restricted') {
                    return '<label class="badge bg-warning">' . __('app.suitable_restricted') . '</label>';
                }
                if ($row->status == 'unsuitable') {
                    return '<label class="badge bg-danger">' . __('app.unsuitable') . '</label>';
                }
                if ($row->status == 'observed') {
                    return '<label class="badge bg-info">' . __('app.observed') . '</label>';
                }
            })
            ->rawColumns(['action', 'status', 'job_applications.full_name', 'checkbox'])
            ->make(true);
    }

    public function dataByCandidate(Request $request, $id)
    {
        abort_if(!$this->user->cans('view_medical_exams'), 403);

        $query = MedicalExam::with('clinic')->select('medical_exams.id', 'clinics.name as clinic_name', 'medical_exams.status', 'medical_exams.comments', 'medical_exams.medical_exam_date')
            ->leftjoin('job_applications', 'job_applications.id', 'medical_exams.job_application_id')
            ->leftjoin('clinics', 'clinics.id', 'medical_exams.clinic_id')
            ->where('medical_exams.job_application_id', $id);

        return DataTables::of($query)
            ->addColumn('action', function ($row) {
                $action = '';
                if ($this->user->cans('view_medical_exams')) {
                    $action .= '<a href="#" class="btn btn-info btn-circle view-data"
                    data-toggle="tooltip" onclick="showMedicalExam(' . $row->id . ')" data-original-title="' . __('app.view') . '"><i class="fa fa-search" aria-hidden="true"></i></a>';
                }
                if ($this->user->cans('edit_medical_exams')) {
                    $action .= '<a href="#" style="margin-left:4px" class="btn btn-primary btn-circle edit-data"
                    data-toggle="tooltip" onclick="editMedicalExam(' . $row->id . ')" data-original-title="' . __('app.edit') . '"><i class="fa fa-pencil" aria-hidden="true"></i></a>';
                }

                return $action;
            })
            ->editColumn('medical_exam_date', function ($row) {
                Carbon::setlocale(config('app.locale'));

                return is_null($row->medical_exam_date) ? null : Carbon::parse($row->medical_exam_date)->translatedFormat('d \de F \de Y');
            })
            ->editColumn('clinic', function ($row) {
                return ucwords($row->clinic_name);
            })
            ->editColumn('status', function ($row) {
                if ($row->status == 'pending') {
                    return '<label class="badge bg-primary">' . __('app.pending') . '</label>';
                }
                if ($row->status == 'scheduled') {
                    return '<label class="badge bg-secondary">' . __('app.scheduled') . '</label>';
                }
                if ($row->status == 'suitable') {
                    return '<label class="badge bg-success">' . __('app.suitable') . '</label>';
                }
                if ($row->status == 'suitable_restricted') {
                    return '<label class="badge bg-warning">' . __('app.suitable_restricted') . '</label>';
                }
                if ($row->status == 'unsuitable') {
                    return '<label class="badge bg-danger">' . __('app.unsuitable') . '</label>';
                }
                if ($row->status == 'observed') {
                    return '<label class="badge bg-info">' . __('app.observed') . '</label>';
                }
            })
            ->rawColumns(['action', 'status', 'clinic'])
            ->make(true);
    }
}
