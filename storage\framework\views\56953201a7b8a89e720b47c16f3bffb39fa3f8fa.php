

<?php $__env->startSection('header-text'); ?>
    <h1 class="hidden-sm-down text-white fs-50 mb-30"><?php if(!is_null($frontTheme->welcome_title)): ?> <?php echo e($frontTheme->welcome_title); ?> <?php else: ?> <?php echo app('translator')->get('modules.front.homeHeader'); ?><?php endif; ?> </h1>
    <h4 class="hidden-sm-up text-white mb-30"> <?php if(!is_null($frontTheme->welcome_title)): ?> <?php echo e($frontTheme->welcome_title); ?> <?php else: ?> <?php echo app('translator')->get('modules.front.homeHeader'); ?><?php endif; ?> </h4>
    
    <div class="location-search d-flex rounded-pill bg-white ">

        <div class="align-items-center d-flex rounded-pill location height-50">
            <select class="myselect" name="loaction" id ="location_id">
                <option value="all"><?php echo app('translator')->get('modules.front.allLocation'); ?></option>
                <?php $__currentLoopData = $locations; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $location): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <option value="<?php echo e($location->id); ?>"><?php echo e(ucfirst($location->location)); ?></option>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </select>
        </div>

        <span class="space position-relative hidden-sm-down "></span>

        <div class="align-items-center d-flex rounded-pill designation height-50">
            <select class="myselect" name="category" id ="category">
                <option value="all"><?php echo app('translator')->get('modules.front.allCategory'); ?></option>
                <?php $__currentLoopData = $categories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $category): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                     <option value="<?php echo e($category->id); ?>"><?php echo e(ucfirst($category->name)); ?></option>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </select>
        </div>

        <div class="align-items-center d-flex rounded-pill designation height-50">
            <select class="myselect" name="company_name" id ="company">
                <option value="all"><?php echo app('translator')->get('modules.front.allCompany'); ?></option>
                <?php $__currentLoopData = $companies; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $company): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                     <option value="<?php echo e($company->id); ?>"><?php echo e(ucfirst($company->company_name)); ?></option>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </select>
        </div>

        <div class="align-items-center d-flex rounded-pill location height-50">
            <select class="myselect" name="name" id ="skill">
                <option value="all"><?php echo app('translator')->get('modules.front.allSkill'); ?></option>
                <?php $__currentLoopData = $skills; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $skill): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <option value="<?php echo e($skill->id); ?>"><?php echo e(ucfirst($skill->name)); ?></option>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </select>
        </div>

        <div class="search-btn w-25 rounded-pill align-items-center ">
            <button type="button" name="search" class="btn btn-lg btn-dark height-48 mr-4 my-1 align-items-center d-flex rounded-pill justify-content-center"  id="search">SEARCH</button>

            
        </div>
    </div>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>



    <!--
    |‒‒‒‒‒‒‒‒‒‒‒‒‒‒‒‒‒‒‒‒‒‒‒‒‒‒‒‒‒‒‒‒‒‒‒‒‒‒‒‒‒‒‒‒‒‒‒‒‒‒‒‒‒‒‒‒‒‒‒‒‒‒‒‒‒‒‒‒‒‒‒‒‒‒
    | Working at TheThemeio
    |‒‒‒‒‒‒‒‒‒‒‒‒‒‒‒‒‒‒‒‒‒‒‒‒‒‒‒‒‒‒‒‒‒‒‒‒‒‒‒‒‒‒‒‒‒‒‒‒‒‒‒‒‒‒‒‒‒‒‒‒‒‒‒‒‒‒‒‒‒‒‒‒‒‒
    !-->
    <!-- <section class="section bg-gray py-60 aaaaa">
        <div class="container">

            <div class="row gap-y align-items-center">

                <div class="col-12">
                    <h3><?php if(!is_null($frontTheme->welcome_title)): ?> <?php echo e($frontTheme->welcome_title); ?> <?php else: ?> <?php echo app('translator')->get('modules.front.jobOpeningHeading'); ?> <?php endif; ?></h3>
                    <p><?php if(!is_null($frontTheme->welcome_sub_title)): ?> <?php echo $frontTheme->welcome_sub_title; ?>  <?php else: ?> <?php echo app('translator')->get('modules.front.jobOpeningText'); ?> <?php endif; ?></p>

                </div>

            </div>

        </div>
    </section> -->





    <!--
    |‒‒‒‒‒‒‒‒‒‒‒‒‒‒‒‒‒‒‒‒‒‒‒‒‒‒‒‒‒‒‒‒‒‒‒‒‒‒‒‒‒‒‒‒‒‒‒‒‒‒‒‒‒‒‒‒‒‒‒‒‒‒‒‒‒‒‒‒‒‒‒‒‒‒
    | Open positions
    |‒‒‒‒‒‒‒‒‒‒‒‒‒‒‒‒‒‒‒‒‒‒‒‒‒‒‒‒‒‒‒‒‒‒‒‒‒‒‒‒‒‒‒‒‒‒‒‒‒‒‒‒‒‒‒‒‒‒‒‒‒‒‒‒‒‒‒‒‒‒‒‒‒‒
    !-->
    <section class="section">
        <div class="container">
            <header class="section-header">
                <h2><?php echo app('translator')->get('modules.front.jobOpenings'); ?></h2>
                <hr>
                <hr>
            </header>


            <div data-provide="shuffle" id="applicant-notes">

               

                
                <div class="row gap-y"  id="jobList">

                    <?php $__empty_1 = true; $__currentLoopData = $jobLocations; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $location): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                        <div class="col-md-12 col-lg-4 portfolio-2 job-list" data-shuffle="item" data-groups="<?php echo e($location->location.','.$location->job->category->name); ?>">
                            <a href="<?php echo e(route('jobs.jobDetail', [$location->job->slug, $location->location->id])); ?>" class="job-opening-card">
                            <div class="card card-bordered">
                                <div class="card-block">
                                    <h5 class="card-title mb-0">
                                        <?php echo e(ucwords($location->job->jobDescription?->name)); ?><br />
                                        <span class="text-sm"><?php echo e(ucwords($location->job->title)); ?></span>
                                    </h5>
                                    <?php if($location->job->company->show_in_frontend == 'true'): ?>
                                        <?php if($location->job->job_company_id != null && $location->job->job_company_id != '' && !is_null($location->job->jobCompany)): ?>
                                             <small class="company-title mb-50"><?php echo app('translator')->get('app.by'); ?> <?php echo e(ucwords($location->job->jobCompany->company_name)); ?></small>
                                        <?php else: ?>
                                            <small class="company-title mb-50"><?php echo app('translator')->get('app.by'); ?> <?php echo e(ucwords($location->job->company->company_name)); ?></small>
                                        <?php endif; ?>
                                    <?php endif; ?>
                                    <div class="d-flex flex-wrap justify-content-between card-location">
                                        <span class="fw-400 fs-14"><i class="mr-5 fa fa-map-marker"></i><?php echo e(ucwords($location->location->location)); ?></span>
                                        <span class="fw-400 fs-14"><?php echo e(ucwords($location->job->category->name)); ?><i class="ml-5 fa fa-graduation-cap"></i></span>
                                    </div>
                                </div>
                            </div>
                            </a>
                        </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                        <h4 id ="no-data" class="mx-auto mt-50 mb-40 card-title mb-0" ><?php echo app('translator')->get('modules.front.noData'); ?> </h4>
                    <?php endif; ?>
                </div>
                
                    <div class="row gap-y">
                        <button type="button" name="load_more_button" class="btn btn-lg btn-white mx-auto mt-50 mb-40"  id="load_more_button"><?php echo app('translator')->get('modules.front.loadMore'); ?></button>
                    </div>   
                
            </div>

            </div>


        </div>
    </section>

<?php $__env->stopSection(); ?>
<?php $__env->startPush('footer-script'); ?>
<script>
    $(document).ready(function(){
        var perPage = '<?php echo e($perPage); ?>';

        totalCurrentData = perPage;
        var jobCount = <?php echo e($jobCount); ?>;
        if(jobCount > perPage)
        {
            $('#load_more_button').show();
        }
        else{
            $('#load_more_button').hide();
        }
        var totalCurrentCount = $(".job-list").length;
        console.log(totalCurrentCount);

        //load more
        $('body').on('click', '#load_more_button', function () {
            var location_id = $('#location_id').val();
            var category = $('#category').val();
            var skill = $('#skill').val();
            var company = $('#company').val();
            console.log("hello");

            var token = '<?php echo e(csrf_token()); ?>';
            $('#load_more_button').html('<b>'+"<?php echo app('translator')->get('app.loading'); ?>"+'...</b>');
            $.easyAjax({
                url:"<?php echo e(route('jobs.more-data')); ?>",
                type:'POST',
                data: {'_token':token, 'totalCurrentData':totalCurrentData,'location_id':location_id, 'category':category, 'skill':skill, 'company':company},
                success:function(response) {
                    $('#jobList').append(response.view);
                    totalCurrentData = response.data.job_current_count;
                    $('#load_more_button').blur();
                    $('#load_more_button').html('<?php echo app('translator')->get('modules.front.loadMore'); ?>');
                    if (response.data.hideButton !== 'undefined' && response.data.hideButton === 'yes'){
                        $('#load_more_button').hide();
                    }
                    if (response.data.hideButton !== 'undefined' && response.data.hideButton === 'no') {
                        $('#load_more_button').show();
                    }
                }
            });
        });

        //search
        $('body').on('click', '#search', function () {
            var location_id = $('#location_id').val();
            var category = $('#category').val();
            var skill = $('#skill').val();
            var company = $('#company').val();
            var token = '<?php echo e(csrf_token()); ?>';
            $.easyAjax({
                url:"<?php echo e(route('jobs.search-job')); ?>",
                type:'POST',
                data: {'_token':token, location_id:location_id, category:category, skill:skill, company:company},
                success:function(response){
                    $('#jobList').html(response.view);
                    totalCurrentData = response.data.job_current_count;
                    $([document.documentElement, document.body]).animate({
                        scrollTop: $("#applicant-notes").offset().top
                    }, 2000);
                    console.log(response.data.hideButton);
                    if (response.data.hideButton != 'undefined' && response.data.hideButton == 'yes'){
                        $('#load_more_button').hide();
                    }
                    if (response.data.hideButton != 'undefined' && response.data.hideButton == 'no') {
                        $('#load_more_button').show();
                    }
                }
            });
        });
    });

    </script>
    <?php $__env->stopPush(); ?>
<?php echo $__env->make('layouts.front', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH E:\laragon\www\recruit-seprocal\resources\views/front/job-openings.blade.php ENDPATH**/ ?>