<?php

namespace App;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class RequiredDocument extends Model
{
    use HasFactory;

    protected $table = 'required_documents';
    protected $fillable = [
        'job_application_id',
        'document_id',
        'documentation_id',
        'is_required',
    ];

    public function job_application()
    {
        return $this->belongsTo(JobApplication::class, 'job_application_id');
    }

    public function documentation()
    {
        return $this->belongsTo(Documentation::class, 'documentation_id');
    }

    public function document()
    {
        return $this->belongsTo(Document::class, 'document_id');
    }
}
