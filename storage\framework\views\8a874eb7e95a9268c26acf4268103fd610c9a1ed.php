

<?php $__env->startPush('head-script'); ?>
  <link href="//cdn.datatables.net/fixedheader/3.1.5/css/fixedHeader.bootstrap.min.css" rel="stylesheet">
  <link href="//cdn.datatables.net/responsive/2.2.3/css/responsive.bootstrap.min.css" rel="stylesheet">
<?php $__env->stopPush(); ?>

<?php if(in_array('add_cost_center', $userPermissions)): ?>
  <?php $__env->startSection('create-button'); ?>
    <a class="btn btn-dark btn-sm m-l-15" href="<?php echo e(route('admin.cost_centers.create')); ?>"><i class="fa fa-plus-circle"></i> <?php echo app('translator')->get('app.createNew'); ?></a>
  <?php $__env->stopSection(); ?>
<?php endif; ?>

<?php $__env->startSection('content'); ?>
  <div class="row">
    <div class="col-12">
      <div class="card">
        <div class="card-body">
          <div class="table-responsive m-t-40">
            <table class="table-bordered table-striped table" id="myTable">
              <thead>
                <tr>
                  <th>#</th>
                  <th><?php echo app('translator')->get('app.code'); ?></th>
                  <th><?php echo app('translator')->get('app.name'); ?></th>
                  <th><?php echo app('translator')->get('app.description'); ?></th>
                  <th><?php echo app('translator')->get('app.action'); ?></th>
                </tr>
              </thead>
            </table>
          </div>
        </div>
      </div>
    </div>
  </div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('footer-script'); ?>
  <script src="//cdn.datatables.net/fixedheader/3.1.5/js/dataTables.fixedHeader.min.js"></script>
  <script src="//cdn.datatables.net/responsive/2.2.3/js/dataTables.responsive.min.js"></script>
  <script src="//cdn.datatables.net/responsive/2.2.3/js/responsive.bootstrap.min.js"></script>

  <script>
    var table = $('#myTable').dataTable({
      responsive: true,
      // processing: true,
      serverSide: true,
      ajax: '<?php echo route('admin.cost_centers.data'); ?>',
      language: languageOptions(),
      "fnDrawCallback": function(oSettings) {
        $("body").tooltip({
          selector: '[data-toggle="tooltip"]'
        });
      },
      columns: [{
          data: 'DT_Row_Index'
        },
        {
          data: 'code',
          name: 'code'
        },
        {
          data: 'name',
          name: 'name'
        },
        {
          data: 'description',
          name: 'description',
          width: '40%'
        },
        {
          data: 'action',
          name: 'action',
          width: '20px',
          className: 'text-nowrap',
          orderable: false,
          searchable: false
        }
      ]
    });

    new $.fn.dataTable.FixedHeader(table);

    $('body').on('click', '.sa-params', function() {
      var id = $(this).data('row-id');
      swal({
        title: "<?php echo app('translator')->get('errors.areYouSure'); ?>",
        text: "<?php echo app('translator')->get('errors.deleteWarning'); ?>",
        type: "warning",
        showCancelButton: true,
        confirmButtonColor: "#DD6B55",
        confirmButtonText: "<?php echo app('translator')->get('app.delete'); ?>",
        cancelButtonText: "<?php echo app('translator')->get('app.cancel'); ?>",
        closeOnConfirm: true,
        closeOnCancel: true
      }, function(isConfirm) {
        if (isConfirm) {

          var url = "<?php echo e(route('admin.cost_centers.destroy', ':id')); ?>";
          url = url.replace(':id', id);

          var token = "<?php echo e(csrf_token()); ?>";

          $.easyAjax({
            type: 'POST',
            url: url,
            data: {
              '_token': token,
              '_method': 'DELETE'
            },
            success: function(response) {
              if (response.status == "success") {
                $.unblockUI();
                table._fnDraw();
              }
            }
          });
        }
      });
    });
  </script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH E:\laragon\www\recruit-seprocal\resources\views/admin/cost_centers/index.blade.php ENDPATH**/ ?>