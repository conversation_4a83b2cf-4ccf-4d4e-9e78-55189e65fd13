<?php

return [
    'dashboard' => 'Dashboard',
    'jobOnboard' => 'Job OnBoard',
    'jobCategories' => 'Job Categories',
    'jobCategory' => 'Job Category',
    'settings' => 'Settings',
    'businessSettings' => 'Business Settings',
    'rolesPermission' => 'Roles & Permissions',
    'skills' => 'Skills',
    'totalPositions' => 'Total Positions',
    'locations' => 'Job Locations',
    'education' => 'Education',
    'jobs' => 'Jobs',
    'jobApplications' => 'Job Applications',
    'currencySetting' => 'Currency Setting',
    'myProfile' => 'My Profile',
    'team' => 'Team',
    'themeSettings' => 'Theme Settings',
    'updateApplication' => 'Update Application',
    'mailSetting' => 'Email Settings',
    'smtpSetting' => 'Smtp Setting',
    'question' => 'Custom Questions',
    'customQuestion' => 'Custom Questions',
    'add' => 'Add',
    'export' => 'Export To Excel',
    'interviewSchedule' => 'Interview Schedule',
    'interviewDate' => 'Interview Date',
    'status' => 'Status',
    'companies' => 'Company',
    'businessSettings' => 'Business Settings',
    'help' => 'Help',
    'sendJobEmails' => 'Send New Job Emails',
    'smsSettings' => 'SMS Settings',
    'securitySettings' => 'Security Settings',
    'profile' => 'Profile',
    'linkedInSettings' => 'Linkedin Settings',
    'storageSetting' => 'Storage Setting',
    'selectStorage' => 'Select Storage',
    'local' => 'Local (Default)',
    'aws' => 'AWS S3 Storage (Amazon Web Services S3)',
    'footerSetting' => 'Footer Setting',
    'footerSettings' => 'Footer Links',
    'todoList' => 'ToDos',
    'jobAlert' => 'Job Alert',
    'reports' => 'Reports',
    'candidateDatabase' => 'Candidate Database',
    'applicationFormSettings' => 'Application Settings',
    'zoom' => 'Zoom',
    'zoomMeeting' => 'Zoom Meeting',
    'zoomSetting' => 'Zoom Setting',
    'postedby' => 'Posted By',
    'cancelReason' => 'Cancel Reason',
    'salary' => 'Salary',
    'projects' => 'Projects',
    'cost_centers' => 'Cost Centers',
    'empleopolis' => 'Empleopolis',
    'filter_date' => 'Filter Date',
    'signus' => 'Signus',
    'adjunct' => 'Annex 04',
    'medical_exam' => 'Examen Médico',
    'medical_exam_date' => 'Fecha Examen Médico',
    'clinics' => 'Clinics'
];
