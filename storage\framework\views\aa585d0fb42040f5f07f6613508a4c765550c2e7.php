<!-- Main Sidebar Container -->
<aside class="main-sidebar sidebar-dark-primary">
  <!-- Brand Logo -->
  <a class="brand-link" href="<?php echo e(route('admin.dashboard')); ?>">
    <img alt="AdminLTE Logo" class="brand-image img-fluid" src="<?php echo e($global->logo_url); ?>">
  </a>

  <!-- Sidebar -->
  <div class="sidebar">

    <!-- Sidebar Menu -->
    <nav class="mt-2">
      <ul class="nav nav-pills nav-sidebar flex-column" data-accordion="false" data-widget="treeview" id="sidebarnav" role="menu">
        <!-- Add icons to the links using the .nav-icon class
           with font-awesome or any other icon font library -->
        <li class="nav-item">
          <a class="nav-link <?php echo e(request()->is('admin/dashboard*') ? 'active' : ''); ?>" href="<?php echo e(route('admin.dashboard')); ?>">
            <i class="nav-icon icon-speedometer"></i>
            <p>
              <?php echo app('translator')->get('menu.dashboard'); ?>
            </p>
          </a>
        </li>

        <?php if(in_array('view_category', $userPermissions)): ?>
          <li class="nav-item">
            <a class="nav-link <?php echo e(request()->is('admin/job-categories*') ? 'active' : ''); ?>" href="<?php echo e(route('admin.job-categories.index')); ?>">
              <i class="nav-icon icon-grid"></i>
              <p>
                <?php echo app('translator')->get('menu.jobCategories'); ?>
              </p>
            </a>
          </li>
        <?php endif; ?>
        <?php if(in_array('view_job_description', $userPermissions)): ?>
          <li class="nav-item">
            <a class="nav-link <?php echo e(request()->is('admin/job_description*') ? 'active' : ''); ?>" href="<?php echo e(route('admin.job_descriptions.index')); ?>">
              <i class="nav-icon icon-briefcase"></i>
              <p>
                <?php echo app('translator')->get('menu.jobDescriptions'); ?>
              </p>
            </a>
          </li>
        <?php endif; ?>
        <?php if(in_array('view_skills', $userPermissions)): ?>
          <li class="nav-item">
            <a class="nav-link <?php echo e(request()->is('admin/skills*') ? 'active' : ''); ?>" href="<?php echo e(route('admin.skills.index')); ?>">
              <i class="nav-icon icon-grid"></i>
              <p>
                <?php echo app('translator')->get('menu.skills'); ?>
              </p>
            </a>
          </li>
        <?php endif; ?>

        <?php if(in_array('view_company', $userPermissions)): ?>
          <li class="nav-item">
            <a class="nav-link <?php echo e(request()->is('admin/company*') ? 'active' : ''); ?>" href="<?php echo e(route('admin.company.index')); ?>">
              <i class="nav-icon icon-film"></i>
              <p>
                <?php echo app('translator')->get('menu.companies'); ?>
              </p>
            </a>
          </li>
        <?php endif; ?>

        <?php if(in_array('view_cost_center', $userPermissions)): ?>
          <li class="nav-item">
            <a class="nav-link <?php echo e(request()->is('admin/cost_centers*') ? 'active' : ''); ?>" href="<?php echo e(route('admin.cost_centers.index')); ?>">
              <i class="nav-icon icon-pie-chart"></i>
              <p><?php echo app('translator')->get('menu.cost_centers'); ?></p>
            </a>
          </li>
        <?php endif; ?>
        <li class="nav-item">
          <a class="nav-link <?php echo e(request()->is('admin/clinic*') ? 'active' : ''); ?>" href="<?php echo e(route('admin.clinics.index')); ?>">
            <i class="nav-icon icon-chemistry"></i>
            <p>
              <?php echo app('translator')->get('menu.clinics'); ?>
            </p>
          </a>
        </li>
        <?php if(in_array('view_locations', $userPermissions)): ?>
          <li class="nav-item">
            <a class="nav-link <?php echo e(request()->is('admin/locations*') ? 'active' : ''); ?>" href="<?php echo e(route('admin.locations.index')); ?>">
              <i class="nav-icon icon-location-pin"></i>
              <p>
                <?php echo app('translator')->get('menu.locations'); ?>
              </p>
            </a>
          </li>
        <?php endif; ?>

        <?php if(in_array('view_jobs', $userPermissions)): ?>
          <li class="nav-item">
            <a class="nav-link <?php echo e(request()->is('admin/jobs*') ? 'active' : ''); ?>" href="<?php echo e(route('admin.jobs.index')); ?>">
              <i class="nav-icon icon-badge"></i>
              <p>
                <?php echo app('translator')->get('menu.jobs'); ?>
              </p>
            </a>
          </li>
        <?php endif; ?>

        <?php if(in_array('view_job_applications', $userPermissions)): ?>
          <li class="nav-item">
            <a class="nav-link <?php echo e(request()->is('admin/job-applications*') ? 'active' : ''); ?>" href="<?php echo e(route('admin.job-applications.index')); ?>">
              <i class="nav-icon icon-user"></i>
              <p>
                <?php echo app('translator')->get('menu.jobApplications'); ?>
              </p>
            </a>
          </li>
          <li class="nav-item">
            <a class="nav-link <?php echo e(request()->is('admin/applications-archive*') ? 'active' : ''); ?>" href="<?php echo e(route('admin.applications-archive.index')); ?>">
              <i class="nav-icon icon-drawer"></i>
              <p>
                <?php echo app('translator')->get('menu.candidateDatabase'); ?>
              </p>
            </a>
          </li>
          <li class="nav-item">
            <a class="nav-link <?php echo e(request()->is('admin/job-onboard*') ? 'active' : ''); ?>" href="<?php echo e(route('admin.job-onboard.index')); ?>">
              <i class="nav-icon icon-user"></i>
              <p>
                <?php echo app('translator')->get('menu.jobOnboard'); ?>
              </p>
            </a>
          </li>
        <?php endif; ?>

        <?php if(in_array('view_schedule', $userPermissions)): ?>
          <li class="nav-item">
            <a class="nav-link <?php echo e(request()->is('admin/interview-schedule*') ? 'active' : ''); ?>" href="<?php echo e(route('admin.interview-schedule.index')); ?>">
              <i class="nav-icon icon-calendar"></i>
              <p>
                <?php echo app('translator')->get('menu.interviewSchedule'); ?>
              </p>
            </a>
          </li>
        <?php endif; ?>

        <?php if(in_array('view_empleopolis', $userPermissions)): ?>
          <li class="nav-item">
            <a class="nav-link <?php echo e(request()->is('admin/empleopolis*') ? 'active' : ''); ?>" href="<?php echo e(route('admin.empleopolis.index')); ?>">
              <i class="nav-icon icon-doc"></i>
              <p>
                Filtro interno
              </p>
            </a>
          </li>
        <?php endif; ?>

        <?php if(in_array('view_signus', $userPermissions)): ?>
          <li class="nav-item">
            <a class="nav-link <?php echo e(request()->is('admin/signus*') ? 'active' : ''); ?>" href="<?php echo e(route('admin.signus.index')); ?>">
              <i class="nav-icon icon-folder-alt"></i>
              <p>
                Filtro del cliente
              </p>
            </a>
          </li>
        <?php endif; ?>

        <?php if(in_array('view_medical_exams', $userPermissions)): ?>
          <li class="nav-item">
            <a class="nav-link <?php echo e(request()->is('admin/medical-exam*') ? 'active' : ''); ?>" href="<?php echo e(route('admin.medical-exam.index')); ?>">
              <i class="nav-icon icon-briefcase"></i>
              <p>
                Examen Médico
              </p>
            </a>
          </li>
        <?php endif; ?>

        <?php if(in_array('view_team', $userPermissions)): ?>
          <li class="nav-item">
            <a class="nav-link <?php echo e(request()->is('admin/team*') ? 'active' : ''); ?>" href="<?php echo e(route('admin.team.index')); ?>">
              <i class="nav-icon icon-people"></i>
              <p>
                <?php echo app('translator')->get('menu.team'); ?>
              </p>
            </a>
          </li>
        <?php endif; ?>

        <?php if($user->roles->count() > 0): ?>
          <li class="nav-item">
            <a class="nav-link <?php echo e(request()->is('admin/todo-items*') ? 'active' : ''); ?>" href="<?php echo e(route('admin.todo-items.index')); ?>">
              <i class="nav-icon icon-notebook"></i>
              <p>
                <?php echo app('translator')->get('menu.todoList'); ?>
              </p>
            </a>
          </li>
        <?php endif; ?>

        <?php if($user->roles->count() > 0): ?>
          <li class="nav-item">
            <a class="nav-link <?php echo e(request()->is('admin/job_alert*') ? 'active' : ''); ?>" href="<?php echo e(route('admin.job_alert.index')); ?>">
              <i class="nav-icon icon-bell"></i>
              <p>
                <?php echo app('translator')->get('menu.jobAlert'); ?>
              </p>
            </a>
          </li>
        <?php endif; ?>

        <?php if(in_array('view_reports', $userPermissions)): ?>
        <li class="nav-item has-treeview <?php if(\Request()->is('admin/reports*') || \Request()->is('admin/report*')): ?> active menu-open <?php endif; ?>">
          <a class="nav-link" href="#">
            <i aria-hidden="true" class="fa fa-bar-chart"></i>
            <p>
              <?php echo app('translator')->get('app.reports'); ?>
              <i class="right fa fa-angle-left"></i>
            </p>
          </a>
          <ul class="nav nav-treeview">
            <li class="nav-item">
              <a class="nav-link <?php echo e(request()->is('admin/reports/candidatos-en-proceso*') ? 'active' : ''); ?>" href="<?php echo e(route('admin.reports.candidatos-en-proceso')); ?>">
                <i class="fa fa-users nav-icon"></i>
                <p>Candidatos en Proceso</p>
              </a>
            </li>
            <li class="nav-item">
              <a class="nav-link <?php echo e(request()->is('admin/reports/historico-contratados*') ? 'active' : ''); ?>" href="<?php echo e(route('admin.reports.historico-contratados')); ?>">
                <i class="fa fa-check nav-icon"></i>
                <p>Histórico de Contratados</p>
              </a>
            </li>
            <li class="nav-item">
              <a class="nav-link <?php echo e(request()->is('admin/reports/requerimientos-por-cubrir*') ? 'active' : ''); ?>" href="<?php echo e(route('admin.reports.requerimientos-por-cubrir')); ?>">
                <i class="fa fa-briefcase nav-icon"></i>
                <p>Requerimientos por Cubrir</p>
              </a>
            </li>
            <li class="nav-item">
              <a class="nav-link <?php echo e(request()->is('admin/reports/historico-cancelados*') ? 'active' : ''); ?>" href="<?php echo e(route('admin.reports.historico-cancelados')); ?>">
                <i class="fa fa-times nav-icon"></i>
                <p>Histórico de Cancelados</p>
              </a>
            </li>
            <li class="nav-item">
              <a class="nav-link <?php echo e(request()->is('admin/reports/candidatos-no-aptos*') ? 'active' : ''); ?>" href="<?php echo e(route('admin.reports.candidatos-no-aptos')); ?>">
                <i class="fa fa-ban nav-icon"></i>
                <p>Candidatos No Aptos</p>
              </a>
            </li>
            <li class="nav-item">
              <a class="nav-link <?php echo e(request()->is('admin/reports/base-general-candidatos*') ? 'active' : ''); ?>" href="<?php echo e(route('admin.reports.base-general-candidatos')); ?>">
                <i class="fa fa-list nav-icon"></i>
                <p>Base General de Candidatos</p>
              </a>
            </li>
            <li class="nav-item">
              <a class="nav-link <?php echo e(request()->is('admin/report*') ? 'active' : ''); ?>" href="<?php echo e(route('admin.report.index')); ?>">
                <i class="fa fa-chart-line nav-icon"></i>
                <p>Reportes del Sistema</p>
              </a>
            </li>
          </ul>
        </li>
        <?php endif; ?>
        <?php if(in_array('view_schedule', $userPermissions)): ?>
          <?php if(isset($zoom_setting->enable_zoom) && $zoom_setting->enable_zoom == 1): ?>
            <li class="nav-item has-treeview">
              <a class="nav-link" href="<?php echo e(route('admin.zoom-meeting.table-view')); ?>">
                <i class="fa fa-video-camera"></i>
                <p> <?php echo app('translator')->get('menu.zoomMeeting'); ?></p>
              </a>
            </li>
          <?php endif; ?>
        <?php endif; ?>
        <li class="nav-item has-treeview <?php if(\Request()->is('admin/settings/*') || \Request()->is('admin/profile')): ?> active menu-open <?php endif; ?>">
          <a class="nav-link" href="#">
            <i class="nav-icon icon-settings"></i>
            <p>
              <?php echo app('translator')->get('menu.settings'); ?>
              <i class="right fa fa-angle-left"></i>
            </p>
          </a>
          <ul class="nav nav-treeview">
            <li class="nav-item">
              <a class="nav-link <?php echo e(request()->is('admin/profile*') ? 'active' : ''); ?>" href="<?php echo e(route('admin.profile.index')); ?>">
                <i class="fa fa-circle-o nav-icon"></i>
                <p> <?php echo app('translator')->get('menu.myProfile'); ?></p>
              </a>
            </li>
            <?php if(in_array('manage_settings', $userPermissions)): ?>
              <li class="nav-item">
                <a class="nav-link <?php echo e(request()->is('admin/settings/settings') ? 'active' : ''); ?>" href="<?php echo e(route('admin.settings.index')); ?>">
                  <i class="fa fa-circle-o nav-icon"></i>
                  <p><?php echo app('translator')->get('menu.businessSettings'); ?></p>
                </a>
              </li>
              <li class="nav-item">
                <a class="nav-link <?php echo e(request()->is('admin/settings/application-setting') ? 'active' : ''); ?>"
                   href="<?php echo e(route('admin.application-setting.index')); ?>">
                  <i class="fa fa-circle-o nav-icon"></i>
                  <p><?php echo app('translator')->get('menu.applicationFormSettings'); ?></p>
                </a>
              </li>
              <li class="nav-item">
                <a class="nav-link <?php echo e(request()->is('admin/settings/currency-settings') ? 'active' : ''); ?>"
                   href="<?php echo e(route('admin.currency-settings.index')); ?>">
                  <i class="fa fa-circle-o nav-icon"></i>
                  <p><?php echo app('translator')->get('menu.currencySetting'); ?></p>
                </a>
              </li>
              <li class="nav-item">
                <a class="nav-link <?php echo e(request()->is('admin/settings/role-permission') ? 'active' : ''); ?>" href="<?php echo e(route('admin.role-permission.index')); ?>">
                  <i class="fa fa-circle-o nav-icon"></i>
                  <p><?php echo app('translator')->get('menu.rolesPermission'); ?></p>
                </a>
              </li>
              <li class="nav-item">
                <a class="nav-link <?php echo e(request()->is('admin/settings/language-settings') ? 'active' : ''); ?>"
                   href="<?php echo e(route('admin.language-settings.index')); ?>">
                  <i class="fa fa-circle-o nav-icon"></i>
                  <p><?php echo app('translator')->get('app.language'); ?> <?php echo app('translator')->get('menu.settings'); ?></p>
                </a>
              </li>
              <li class="nav-item">
                <a class="nav-link <?php echo e(request()->is('admin/settings/footer-settings') ? 'active' : ''); ?>" href="<?php echo e(route('admin.footer-settings.index')); ?>">
                  <i class="fa fa-circle-o nav-icon"></i>
                  <p><?php echo app('translator')->get('menu.footerSettings'); ?></p>
                </a>
              </li>
              <li class="nav-item">
                <a class="nav-link <?php echo e(request()->is('admin/settings/theme-settings') ? 'active' : ''); ?>" href="<?php echo e(route('admin.theme-settings.index')); ?>">
                  <i class="fa fa-circle-o nav-icon"></i>
                  <p><?php echo app('translator')->get('menu.themeSettings'); ?></p>
                </a>
              </li>
              <li class="nav-item">
                <a class="nav-link <?php echo e(request()->is('admin/settings/smtp-settings') ? 'active' : ''); ?>" href="<?php echo e(route('admin.smtp-settings.index')); ?>">
                  <i class="fa fa-circle-o nav-icon"></i>
                  <p><?php echo app('translator')->get('menu.mailSetting'); ?></p>
                </a>
              </li>
              <li class="nav-item">
                <a class="nav-link <?php echo e(request()->is('admin/settings/sms-settings') ? 'active' : ''); ?>" href="<?php echo e(route('admin.sms-settings.index')); ?>">
                  <i class="fa fa-circle-o nav-icon"></i>
                  <p><?php echo app('translator')->get('menu.smsSettings'); ?></p>
                </a>
              </li>
              <li class="nav-item">
                <a class="nav-link" href="<?php echo e(route('admin.storage-settings.index')); ?>">
                  <i class="fa fa-circle-o nav-icon"></i>
                  <p><?php echo app('translator')->get('menu.storageSetting'); ?></p>
                </a>
              </li>
              <li class="nav-item">
                <a class="nav-link <?php echo e(request()->is('admin/settings/sms-settings') ? 'active' : ''); ?>" href="<?php echo e(route('admin.security-setting.index')); ?>">
                  <i class="fa fa-circle-o nav-icon"></i>
                  <p><?php echo app('translator')->get('menu.securitySettings'); ?></p>
                </a>
              </li>

              <li class="nav-item">
                <a class="nav-link" href="<?php echo e(route('admin.linkedin-settings.index')); ?>">
                  <i class="fa fa-circle-o nav-icon"></i>
                  <p><?php echo app('translator')->get('menu.linkedInSettings'); ?></p>
                </a>
              </li>
              <?php if($global->system_update == 1): ?>
                <li class="nav-item">
                  <a class="nav-link <?php echo e(request()->is('admin/settings/update-application') ? 'active' : ''); ?>"
                     href="<?php echo e(route('admin.update-application.index')); ?>">
                    <i class="fa fa-circle-o nav-icon"></i>
                    <p><?php echo app('translator')->get('menu.updateApplication'); ?></p>
                  </a>
                </li>
              <?php endif; ?>
              <li class="nav-item">
                <a class="nav-link" href="<?php echo e(route('admin.zoom-setting.index')); ?>">
                  <i class="fa fa-circle-o nav-icon"></i>

                  <p> <?php echo app('translator')->get('menu.zoomSetting'); ?></p>
                </a>
              </li>
              <li class="nav-item">
                <a class="nav-link" href="https://froiden.freshdesk.com/support/solutions/" target="_blank">
                  <i class="fa fa-circle-o nav-icon"></i>
                  <p><?php echo app('translator')->get('menu.help'); ?></p>
                </a>
              </li>

            <?php endif; ?>


          </ul>
        </li>

        <li class="nav-header">MISCELLANEOUS</li>
        <li class="nav-item">
          <a class="nav-link" href="<?php echo e(url('/')); ?>" target="_blank">
            <i class="nav-icon fa fa-external-link"></i>
            <p>Front Website</p>
          </a>
        </li>

      </ul>
    </nav>
    <!-- /.sidebar-menu -->
  </div>
  <!-- /.sidebar -->
</aside>
<?php /**PATH E:\laragon\www\recruit-seprocal\resources\views/sections/left-sidebar.blade.php ENDPATH**/ ?>