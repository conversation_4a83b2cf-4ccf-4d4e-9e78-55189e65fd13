<?php

namespace App\Http\Controllers\Admin;

use App\Job;
use App\Signus;
use Carbon\Carbon;
use App\Empleopolis;
use App\MedicalExam;
use App\JobApplication;
use App\ApplicationStatus;
use App\InterviewSchedule;
use Illuminate\Http\Request;
use Yajra\DataTables\Facades\DataTables;
use App\Http\Controllers\Admin\AdminBaseController;
use Maatwebsite\Excel\Facades\Excel;
use App\Exports\ReportsExport;
use Barryvdh\DomPDF\Facade\Pdf;


class AdminReportsController extends AdminBaseController
{
    public function __construct()
    {
        parent::__construct();
        $this->pageTitle = __('menu.reports');
        $this->pageIcon = 'icon-chart';
    }

    /**
     * Mostrar el índice de reportes
     */
    public function index()
    {
        abort_if(!$this->user->cans('view_reports'), 403);
        return view('admin.reports.index', $this->data);
    }

    /**
     * REPORTE 1: BASE DE CANDIDATOS EN PROCESO
     * Candidatos que están actualmente en proceso de selección
     */
    public function candidatosEnProceso()
    {
        abort_if(!$this->user->cans('view_reports'), 403);
        $this->pageTitle = 'Base de Candidatos en Proceso';
        return view('admin.reports.candidatos-en-proceso', $this->data);
    }

    public function candidatosEnProcesoData(Request $request)
    {
        abort_if(!$this->user->cans('view_reports'), 403);

        // Estados que consideramos "en proceso" (excluyendo contratado y rechazado)
        $estadosEnProceso = ApplicationStatus::whereNotIn('status', ['contratado', 'rechazado'])->pluck('id');

        $candidatos = JobApplication::select([
                'job_applications.id',
                'job_applications.full_name',
                'job_applications.email',
                'job_applications.phone',
                'job_applications.gender',
                'job_applications.dob',
                'job_applications.address',
                'job_applications.created_at',
                'job_applications.updated_at',
                'jobs.title as puesto',
                'job_descriptions.name as descripcion_puesto',
                'job_categories.name as categoria_puesto',
                'cost_centers.name as centro_costo',
                'cost_centers.code as codigo_centro_costo',
                'job_locations.location as unidad_minera',
                'application_status.status as etapa_actual',
                'application_status.title as titulo_etapa',
                'jobs.starting_salary',
                'jobs.maximum_salary',
                'jobs.pay_type',
                'users.name as responsable_proceso'
            ])
            ->join('jobs', 'job_applications.job_id', '=', 'jobs.id')
            ->leftJoin('job_descriptions', 'jobs.job_description_id', '=', 'job_descriptions.id')
            ->leftJoin('job_categories', 'jobs.category_id', '=', 'job_categories.id')
            ->leftJoin('cost_centers', 'jobs.cost_center_id', '=', 'cost_centers.id')
            ->leftJoin('job_job_locations', 'jobs.id', '=', 'job_job_locations.job_id')
            ->leftJoin('job_locations', 'job_job_locations.location_id', '=', 'job_locations.id')
            ->leftJoin('application_status', 'job_applications.status_id', '=', 'application_status.id')
            ->leftJoin('users', 'jobs.created_by_user_id', '=', 'users.id')
            ->whereIn('job_applications.status_id', $estadosEnProceso)
            ->whereNull('job_applications.deleted_at');

        // Filtros de fecha
        if ($request->filled('start_date')) {
            $candidatos->whereDate('job_applications.created_at', '>=', $request->start_date);
        }

        if ($request->filled('end_date')) {
            $candidatos->whereDate('job_applications.created_at', '<=', $request->end_date);
        }

        $candidatos = $candidatos->groupBy('job_applications.id');

        return DataTables::of($candidatos)
            ->addIndexColumn()
            ->addColumn('datos_personales', function ($row) {
                return "Nombre: {$row->full_name}<br>Email: {$row->email}<br>Teléfono: {$row->phone}<br>Género: {$row->gender}<br>Fecha Nac: " . ($row->dob ? Carbon::parse($row->dob)->format('d/m/Y') : 'N/A');
            })
            ->addColumn('unidad_centro_costo', function ($row) {
                return "Unidad: {$row->unidad_minera}<br>Centro Costo: {$row->centro_costo} ({$row->codigo_centro_costo})";
            })
            ->addColumn('puesto_categoria', function ($row) {
                $categoria = $this->determinarCategoriaPuesto($row->categoria_puesto);
                $puesto_completo = $row->puesto;
                if ($row->descripcion_puesto) {
                    $puesto_completo .= " - " . $row->descripcion_puesto;
                }
                return "Puesto: {$puesto_completo}<br>Categoría: {$categoria}";
            })
            ->addColumn('condiciones_contratacion', function ($row) {
                $salario = $row->starting_salary ? "S/ {$row->starting_salary}" : 'No especificado';
                if ($row->maximum_salary && $row->maximum_salary != $row->starting_salary) {
                    $salario .= " - S/ {$row->maximum_salary}";
                }
                return "Tipo: {$row->pay_type}<br>Salario: {$salario}";
            })
            ->addColumn('fecha_etapa', function ($row) {
                return Carbon::parse($row->updated_at)->format('d/m/Y H:i');
            })
            ->rawColumns(['datos_personales', 'unidad_centro_costo', 'puesto_categoria', 'condiciones_contratacion'])
            ->make(true);
    }

    /**
     * REPORTE 2: BASE HISTÓRICA DE REQUERIMIENTOS CUBIERTOS
     * Candidatos que fueron reclutados y subieron a unidad minera
     */
    public function historicoContratados()
    {
        abort_if(!$this->user->cans('view_reports'), 403);
        $this->pageTitle = 'Base Histórica de Requerimientos Cubiertos';
        return view('admin.reports.historico-contratados', $this->data);
    }

    public function historicoContratadosData(Request $request)
    {
        abort_if(!$this->user->cans('view_reports'), 403);

        // Estado "contratado"
        $estadoContratado = ApplicationStatus::where('status', 'contratado')->first();

        $candidatos = JobApplication::select([
                'job_applications.id',
                'job_applications.full_name',
                'job_applications.email',
                'job_applications.phone',
                'job_applications.gender',
                'job_applications.dob',
                'job_applications.address',
                'job_applications.created_at',
                'job_applications.updated_at',
                'jobs.title as puesto',
                'job_descriptions.name as descripcion_puesto',
                'job_categories.name as categoria_puesto',
                'cost_centers.name as centro_costo',
                'cost_centers.code as codigo_centro_costo',
                'job_locations.location as unidad_minera',
                'jobs.starting_salary',
                'jobs.maximum_salary',
                'jobs.pay_type',
                'users.name as responsable_proceso',
                'on_board_details.joining_date as fecha_subida',
                'on_board_details.salary_offered'
            ])
            ->join('jobs', 'job_applications.job_id', '=', 'jobs.id')
            ->leftJoin('job_descriptions', 'jobs.job_description_id', '=', 'job_descriptions.id')
            ->leftJoin('job_categories', 'jobs.category_id', '=', 'job_categories.id')
            ->leftJoin('cost_centers', 'jobs.cost_center_id', '=', 'cost_centers.id')
            ->leftJoin('job_job_locations', 'jobs.id', '=', 'job_job_locations.job_id')
            ->leftJoin('job_locations', 'job_job_locations.location_id', '=', 'job_locations.id')
            ->leftJoin('users', 'jobs.created_by_user_id', '=', 'users.id')
            ->leftJoin('on_board_details', 'job_applications.id', '=', 'on_board_details.job_application_id')
            ->where('job_applications.status_id', $estadoContratado->id)
            ->whereNull('job_applications.deleted_at');

        // Filtros de fecha
        if ($request->filled('start_date')) {
            $candidatos->whereDate('on_board_details.joining_date', '>=', $request->start_date);
        }

        if ($request->filled('end_date')) {
            $candidatos->whereDate('on_board_details.joining_date', '<=', $request->end_date);
        }

        $candidatos = $candidatos->groupBy('job_applications.id');

        return DataTables::of($candidatos)
            ->addIndexColumn()
            ->addColumn('datos_personales', function ($row) {
                return "Nombre: {$row->full_name}<br>Email: {$row->email}<br>Teléfono: {$row->phone}<br>Género: {$row->gender}<br>Fecha Nac: " . ($row->dob ? Carbon::parse($row->dob)->format('d/m/Y') : 'N/A');
            })
            ->addColumn('unidad_centro_costo', function ($row) {
                return "Unidad: {$row->unidad_minera}<br>Centro Costo: {$row->centro_costo} ({$row->codigo_centro_costo})";
            })
            ->addColumn('puesto_categoria', function ($row) {
                $categoria = $this->determinarCategoriaPuesto($row->categoria_puesto);
                $puesto_completo = $row->puesto;
                if ($row->descripcion_puesto) {
                    $puesto_completo .= " - " . $row->descripcion_puesto;
                }
                return "Puesto: {$puesto_completo}<br>Categoría: {$categoria}";
            })
            ->addColumn('condiciones_contratacion', function ($row) {
                $salario = $row->salary_offered ? "S/ {$row->salary_offered}" : ($row->starting_salary ? "S/ {$row->starting_salary}" : 'No especificado');
                return "Tipo: {$row->pay_type}<br>Salario: {$salario}";
            })
            ->addColumn('fecha_subida_formatted', function ($row) {
                return $row->fecha_subida ? Carbon::parse($row->fecha_subida)->format('d/m/Y') : 'No especificada';
            })
            ->rawColumns(['datos_personales', 'unidad_centro_costo', 'puesto_categoria', 'condiciones_contratacion'])
            ->make(true);
    }

    /**
     * REPORTE 3: REQUERIMIENTOS POR CUBRIR
     * Trabajos activos que aún necesitan candidatos
     */
    public function requerimientosPorCubrir()
    {
        abort_if(!$this->user->cans('view_reports'), 403);
        $this->pageTitle = 'Requerimientos por Cubrir';
        return view('admin.reports.requerimientos-por-cubrir', $this->data);
    }

    public function requerimientosPorCubrirData(Request $request)
    {
        abort_if(!$this->user->cans('view_reports'), 403);

        $trabajos = Job::select([
                'jobs.id',
                'jobs.code',
                'jobs.title as puesto',
                'job_descriptions.name as descripcion_puesto',
                'jobs.total_positions',
                'jobs.start_date',
                'jobs.created_at',
                'job_categories.name as categoria_puesto',
                'cost_centers.name as centro_costo',
                'cost_centers.code as codigo_centro_costo',
                'job_locations.location as unidad_minera'
            ])
            ->leftJoin('job_descriptions', 'jobs.job_description_id', '=', 'job_descriptions.id')
            ->leftJoin('job_categories', 'jobs.category_id', '=', 'job_categories.id')
            ->leftJoin('cost_centers', 'jobs.cost_center_id', '=', 'cost_centers.id')
            ->leftJoin('job_job_locations', 'jobs.id', '=', 'job_job_locations.job_id')
            ->leftJoin('job_locations', 'job_job_locations.location_id', '=', 'job_locations.id')
            ->where('jobs.status', 'active');

        // Filtros de fecha
        if ($request->filled('start_date')) {
            $trabajos->whereDate('jobs.start_date', '>=', $request->start_date);
        }

        if ($request->filled('end_date')) {
            $trabajos->whereDate('jobs.start_date', '<=', $request->end_date);
        }

        $trabajos = $trabajos->groupBy('jobs.id');

        return DataTables::of($trabajos)
            ->addIndexColumn()
            ->addColumn('semana_apertura', function ($row) {
                return $this->calcularSemana($row->start_date);
            })
            ->addColumn('fecha_apertura', function ($row) {
                return Carbon::parse($row->start_date)->format('d/m/Y');
            })
            ->addColumn('unidad_centro_costo', function ($row) {
                return "Unidad: {$row->unidad_minera}<br>Centro Costo: {$row->centro_costo} ({$row->codigo_centro_costo})";
            })
            ->addColumn('puesto_categoria', function ($row) {
                $categoria = $this->determinarCategoriaPuesto($row->categoria_puesto);
                $puesto_completo = $row->puesto;
                if ($row->descripcion_puesto) {
                    $puesto_completo .= " - " . $row->descripcion_puesto;
                }
                return "Puesto: {$puesto_completo}<br>Categoría: {$categoria}";
            })
            ->addColumn('vacantes_solicitadas', function ($row) {
                return $row->total_positions;
            })
            ->rawColumns(['unidad_centro_costo', 'puesto_categoria'])
            ->make(true);
    }

    /**
     * REPORTE 4: BASE HISTÓRICA DE REQUERIMIENTOS CANCELADOS
     * Trabajos que fueron cancelados
     */
    public function historicoCancelados()
    {
        abort_if(!$this->user->cans('view_reports'), 403);
        $this->pageTitle = 'Base Histórica de Requerimientos Cancelados';
        return view('admin.reports.historico-cancelados', $this->data);
    }

    public function historicoCanceladosData(Request $request)
    {
        abort_if(!$this->user->cans('view_reports'), 403);

        $trabajos = Job::select([
                'jobs.id',
                'jobs.code',
                'jobs.title as puesto',
                'job_descriptions.name as descripcion_puesto',
                'jobs.total_positions',
                'jobs.updated_at',
                'jobs.canceled_reason',
                'job_categories.name as categoria_puesto',
                'cost_centers.name as centro_costo',
                'cost_centers.code as codigo_centro_costo',
                'job_locations.location as unidad_minera'
            ])
            ->leftJoin('job_descriptions', 'jobs.job_description_id', '=', 'job_descriptions.id')
            ->leftJoin('job_categories', 'jobs.category_id', '=', 'job_categories.id')
            ->leftJoin('cost_centers', 'jobs.cost_center_id', '=', 'cost_centers.id')
            ->leftJoin('job_job_locations', 'jobs.id', '=', 'job_job_locations.job_id')
            ->leftJoin('job_locations', 'job_job_locations.location_id', '=', 'job_locations.id')
            ->where('jobs.status', 'canceled');

        // Filtros de fecha
        if ($request->filled('start_date')) {
            $trabajos->whereDate('jobs.updated_at', '>=', $request->start_date);
        }

        if ($request->filled('end_date')) {
            $trabajos->whereDate('jobs.updated_at', '<=', $request->end_date);
        }

        $trabajos = $trabajos->groupBy('jobs.id');

        return DataTables::of($trabajos)
            ->addIndexColumn()
            ->addColumn('semana_cancelacion', function ($row) {
                return $this->calcularSemana($row->updated_at);
            })
            ->addColumn('fecha_cancelacion', function ($row) {
                return Carbon::parse($row->updated_at)->format('d/m/Y');
            })
            ->addColumn('motivo_cancelacion', function ($row) {
                return $row->canceled_reason ?: 'No especificado';
            })
            ->addColumn('unidad_centro_costo', function ($row) {
                return "Unidad: {$row->unidad_minera}<br>Centro Costo: {$row->centro_costo} ({$row->codigo_centro_costo})";
            })
            ->addColumn('puesto_categoria', function ($row) {
                $categoria = $this->determinarCategoriaPuesto($row->categoria_puesto);
                $puesto_completo = $row->puesto;
                if ($row->descripcion_puesto) {
                    $puesto_completo .= " - " . $row->descripcion_puesto;
                }
                return "Puesto: {$puesto_completo}<br>Categoría: {$categoria}";
            })
            ->addColumn('vacantes_canceladas', function ($row) {
                return $row->total_positions;
            })
            ->rawColumns(['unidad_centro_costo', 'puesto_categoria'])
            ->make(true);
    }

    /**
     * REPORTE 5: BASE HISTÓRICA DE CANDIDATOS NO APTOS
     * Candidatos que fueron rechazados
     */
    public function candidatosNoAptos()
    {
        abort_if(!$this->user->cans('view_reports'), 403);
        $this->pageTitle = 'Base Histórica de Candidatos No Aptos';
        return view('admin.reports.candidatos-no-aptos', $this->data);
    }

    public function candidatosNoAptosData(Request $request)
    {
        abort_if(!$this->user->cans('view_reports'), 403);

        // Estado "rechazado"
        $estadoRechazado = ApplicationStatus::where('status', 'rechazado')->first();

        $candidatos = JobApplication::select([
                'job_applications.id',
                'job_applications.full_name',
                'job_applications.email',
                'job_applications.phone',
                'job_applications.gender',
                'job_applications.dob',
                'job_applications.address',
                'job_applications.updated_at',
                'jobs.title as puesto',
                'job_descriptions.name as descripcion_puesto',
                'job_categories.name as categoria_puesto',
                'cost_centers.name as centro_costo',
                'cost_centers.code as codigo_centro_costo',
                'job_locations.location as unidad_minera'
            ])
            ->join('jobs', 'job_applications.job_id', '=', 'jobs.id')
            ->leftJoin('job_descriptions', 'jobs.job_description_id', '=', 'job_descriptions.id')
            ->leftJoin('job_categories', 'jobs.category_id', '=', 'job_categories.id')
            ->leftJoin('cost_centers', 'jobs.cost_center_id', '=', 'cost_centers.id')
            ->leftJoin('job_job_locations', 'jobs.id', '=', 'job_job_locations.job_id')
            ->leftJoin('job_locations', 'job_job_locations.location_id', '=', 'job_locations.id')
            ->where('job_applications.status_id', $estadoRechazado->id)
            ->whereNull('job_applications.deleted_at');

        // Filtros de fecha
        if ($request->filled('start_date')) {
            $candidatos->whereDate('job_applications.updated_at', '>=', $request->start_date);
        }

        if ($request->filled('end_date')) {
            $candidatos->whereDate('job_applications.updated_at', '<=', $request->end_date);
        }

        $candidatos = $candidatos->groupBy('job_applications.id');

        return DataTables::of($candidatos)
            ->addIndexColumn()
            ->addColumn('datos_personales', function ($row) {
                return "Nombre: {$row->full_name}<br>Email: {$row->email}<br>Teléfono: {$row->phone}<br>Género: {$row->gender}<br>Fecha Nac: " . ($row->dob ? Carbon::parse($row->dob)->format('d/m/Y') : 'N/A');
            })
            ->addColumn('unidad_centro_costo', function ($row) {
                return "Unidad: {$row->unidad_minera}<br>Centro Costo: {$row->centro_costo} ({$row->codigo_centro_costo})";
            })
            ->addColumn('puesto_categoria', function ($row) {
                $categoria = $this->determinarCategoriaPuesto($row->categoria_puesto);
                $puesto_completo = $row->puesto;
                if ($row->descripcion_puesto) {
                    $puesto_completo .= " - " . $row->descripcion_puesto;
                }
                return "Puesto: {$puesto_completo}<br>Categoría: {$categoria}";
            })
            ->addColumn('motivo_no_aptitud', function ($row) {
                // Buscar el motivo en las diferentes etapas del proceso
                $motivo = $this->obtenerMotivoRechazo($row->id);
                return $motivo ?: 'No especificado';
            })
            ->addColumn('fecha_rechazo', function ($row) {
                return Carbon::parse($row->updated_at)->format('d/m/Y H:i');
            })
            ->rawColumns(['datos_personales', 'unidad_centro_costo', 'puesto_categoria'])
            ->make(true);
    }

    /**
     * REPORTE 6: BASE GENERAL DE CANDIDATOS
     * Todos los candidatos del sistema
     */
    public function baseGeneralCandidatos()
    {
        abort_if(!$this->user->cans('view_reports'), 403);
        $this->pageTitle = 'Base General de Candidatos';
        return view('admin.reports.base-general-candidatos', $this->data);
    }

    public function baseGeneralCandidatosData(Request $request)
    {
        abort_if(!$this->user->cans('view_reports'), 403);

        $candidatos = JobApplication::select([
                'job_applications.id',
                'job_applications.full_name',
                'job_applications.email',
                'job_applications.phone',
                'job_applications.gender',
                'job_applications.dob',
                'job_applications.address',
                'job_applications.created_at',
                'job_applications.updated_at',
                'jobs.title as puesto',
                'job_descriptions.name as descripcion_puesto',
                'job_categories.name as categoria_puesto',
                'cost_centers.name as centro_costo',
                'cost_centers.code as codigo_centro_costo',
                'job_locations.location as unidad_minera',
                'application_status.status as etapa_actual',
                'application_status.title as titulo_etapa',
                'users.name as responsable_proceso'
            ])
            ->join('jobs', 'job_applications.job_id', '=', 'jobs.id')
            ->leftJoin('job_descriptions', 'jobs.job_description_id', '=', 'job_descriptions.id')
            ->leftJoin('job_categories', 'jobs.category_id', '=', 'job_categories.id')
            ->leftJoin('cost_centers', 'jobs.cost_center_id', '=', 'cost_centers.id')
            ->leftJoin('job_job_locations', 'jobs.id', '=', 'job_job_locations.job_id')
            ->leftJoin('job_locations', 'job_job_locations.location_id', '=', 'job_locations.id')
            ->leftJoin('application_status', 'job_applications.status_id', '=', 'application_status.id')
            ->leftJoin('users', 'jobs.created_by_user_id', '=', 'users.id')
            ->whereNull('job_applications.deleted_at');

        // Filtros de fecha
        if ($request->filled('start_date')) {
            $candidatos->whereDate('job_applications.created_at', '>=', $request->start_date);
        }

        if ($request->filled('end_date')) {
            $candidatos->whereDate('job_applications.created_at', '<=', $request->end_date);
        }

        $candidatos = $candidatos->groupBy('job_applications.id');

        return DataTables::of($candidatos)
            ->addIndexColumn()
            ->addColumn('datos_personales', function ($row) {
                return "Nombre: {$row->full_name}<br>Email: {$row->email}<br>Teléfono: {$row->phone}<br>Género: {$row->gender}<br>Fecha Nac: " . ($row->dob ? Carbon::parse($row->dob)->format('d/m/Y') : 'N/A');
            })
            ->addColumn('unidad_centro_costo', function ($row) {
                return "Unidad: {$row->unidad_minera}<br>Centro Costo: {$row->centro_costo} ({$row->codigo_centro_costo})";
            })
            ->addColumn('puesto_categoria', function ($row) {
                $categoria = $this->determinarCategoriaPuesto($row->categoria_puesto);
                $puesto_completo = $row->puesto;
                if ($row->descripcion_puesto) {
                    $puesto_completo .= " - " . $row->descripcion_puesto;
                }
                return "Puesto: {$puesto_completo}<br>Categoría: {$categoria}";
            })
            ->addColumn('etapa_estado', function ($row) {
                return $row->titulo_etapa ?: $row->etapa_actual;
            })
            ->addColumn('fecha_etapa', function ($row) {
                return Carbon::parse($row->updated_at)->format('d/m/Y H:i');
            })
            ->rawColumns(['datos_personales', 'unidad_centro_costo', 'puesto_categoria'])
            ->make(true);
    }

    /**
     * Función auxiliar para obtener motivo de rechazo
     */
    private function obtenerMotivoRechazo($jobApplicationId)
    {
        // Buscar en entrevistas
        $entrevista = InterviewSchedule::where('job_application_id', $jobApplicationId)
            ->where('status', 'rejected')
            ->latest()
            ->first();

        if ($entrevista) {
            return 'Rechazado en entrevista';
        }

        // Buscar en empleopolis
        $empleopolis = Empleopolis::where('job_application_id', $jobApplicationId)
            ->where('status', 'observed')
            ->latest()
            ->first();

        if ($empleopolis && $empleopolis->comments) {
            return 'Filtro interno: ' . $empleopolis->comments;
        }

        // Buscar en signus
        $signus = Signus::where('job_application_id', $jobApplicationId)
            ->where('status', 'observed')
            ->latest()
            ->first();

        if ($signus && $signus->comments) {
            return 'Filtro cliente: ' . $signus->comments;
        }

        // Buscar en examen médico
        $medicalExam = MedicalExam::where('job_application_id', $jobApplicationId)
            ->where('status', 'observed')
            ->latest()
            ->first();

        if ($medicalExam && $medicalExam->comments) {
            return 'Examen médico: ' . $medicalExam->comments;
        }

        return 'Motivo no especificado';
    }

    /**
     * Función auxiliar para determinar categoría del puesto
     */
    private function determinarCategoriaPuesto($categoria)
    {
        if (!$categoria) {
            return 'No especificada';
        }

        // Lógica para determinar si es OBRERO o EMPLEADO basado en la categoría
        $categorias_obreros = ['operario', 'obrero', 'técnico', 'operador', 'ayudante'];
        $categoria_lower = strtolower($categoria);

        foreach ($categorias_obreros as $cat_obrero) {
            if (strpos($categoria_lower, $cat_obrero) !== false) {
                return 'OBRERO';
            }
        }

        return 'EMPLEADO';
    }

    /**
     * Función auxiliar para calcular semana del año
     */
    private function calcularSemana($fecha)
    {
        return 'Semana ' . Carbon::parse($fecha)->weekOfYear;
    }

    /**
     * EXPORTACIONES
     */
    public function exportCandidatosEnProceso(Request $request)
    {
        abort_if(!$this->user->cans('view_reports'), 403);

        $data = $this->getCandidatosEnProcesoData($request);
        $headings = ['#', 'Datos Personales', 'Unidad Minera / Centro de Costo', 'Puesto / Categoría', 'Etapa Actual', 'Fecha de Etapa', 'Condiciones de Contratación', 'Responsable del Proceso'];
        $title = 'Base de Candidatos en Proceso - ' . Carbon::now()->format('d-m-Y');

        return Excel::download(new ReportsExport($data, $headings, $title, 'candidatos'), $title . '.xlsx');
    }

    public function exportHistoricoContratados(Request $request)
    {
        abort_if(!$this->user->cans('view_reports'), 403);

        $data = $this->getHistoricoContratadosData($request);
        $headings = ['#', 'Datos Personales', 'Unidad Minera / Centro de Costo', 'Puesto / Categoría', 'Condiciones de Contratación', 'Fecha de Subida', 'Responsable del Proceso'];
        $title = 'Base Histórica de Requerimientos Cubiertos - ' . Carbon::now()->format('d-m-Y');

        return Excel::download(new ReportsExport($data, $headings, $title, 'contratados'), $title . '.xlsx');
    }

    public function exportRequerimientosPorCubrir(Request $request)
    {
        abort_if(!$this->user->cans('view_reports'), 403);

        $data = $this->getRequerimientosPorCubrirData($request);
        $headings = ['#', 'Semana de Apertura', 'Fecha de Apertura', 'Unidad Minera / Centro de Costo', 'Puesto / Categoría', 'Cantidad de Vacantes'];
        $title = 'Requerimientos por Cubrir - ' . Carbon::now()->format('d-m-Y');

        return Excel::download(new ReportsExport($data, $headings, $title, 'requerimientos'), $title . '.xlsx');
    }

    public function exportHistoricoCancelados(Request $request)
    {
        abort_if(!$this->user->cans('view_reports'), 403);

        $data = $this->getHistoricoCanceladosData($request);
        $headings = ['#', 'Semana de Cancelación', 'Fecha de Cancelación', 'Motivo de Cancelación', 'Unidad Minera / Centro de Costo', 'Puesto / Categoría', 'Vacantes Canceladas'];
        $title = 'Base Histórica de Requerimientos Cancelados - ' . Carbon::now()->format('d-m-Y');

        return Excel::download(new ReportsExport($data, $headings, $title, 'cancelados'), $title . '.xlsx');
    }

    public function exportCandidatosNoAptos(Request $request)
    {
        abort_if(!$this->user->cans('view_reports'), 403);

        $data = $this->getCandidatosNoAptosData($request);
        $headings = ['#', 'Datos Personales', 'Unidad Minera / Centro de Costo', 'Puesto / Categoría', 'Motivo de No Aptitud', 'Fecha de Rechazo'];
        $title = 'Base Histórica de Candidatos No Aptos - ' . Carbon::now()->format('d-m-Y');

        return Excel::download(new ReportsExport($data, $headings, $title, 'no_aptos'), $title . '.xlsx');
    }

    public function exportBaseGeneralCandidatos(Request $request)
    {
        abort_if(!$this->user->cans('view_reports'), 403);

        $data = $this->getBaseGeneralCandidatosData($request);
        $headings = ['#', 'Datos Personales', 'Unidad Minera / Centro de Costo', 'Puesto / Categoría', 'Etapa / Estado', 'Fecha de Etapa', 'Responsable del Proceso'];
        $title = 'Base General de Candidatos - ' . Carbon::now()->format('d-m-Y');

        return Excel::download(new ReportsExport($data, $headings, $title, 'candidatos'), $title . '.xlsx');
    }

    /**
     * EXPORTACIONES PDF
     */
    public function exportCandidatosEnProcesoPdf(Request $request)
    {
        abort_if(!$this->user->cans('view_reports'), 403);

        $data = $this->getCandidatosEnProcesoData($request);
        $title = 'Base de Candidatos en Proceso - ' . Carbon::now()->format('d-m-Y');

        $pdf = Pdf::loadView('admin.reports.pdf.candidatos-en-proceso', compact('data', 'title'))
                  ->setPaper('a4', 'landscape');

        return $pdf->download($title . '.pdf');
    }

    public function exportHistoricoContratadosPdf(Request $request)
    {
        abort_if(!$this->user->cans('view_reports'), 403);

        $data = $this->getHistoricoContratadosData($request);
        $title = 'Base Histórica de Requerimientos Cubiertos - ' . Carbon::now()->format('d-m-Y');

        $pdf = Pdf::loadView('admin.reports.pdf.historico-contratados', compact('data', 'title'))
                  ->setPaper('a4', 'landscape');

        return $pdf->download($title . '.pdf');
    }

    public function exportRequerimientosPorCubrirPdf(Request $request)
    {
        abort_if(!$this->user->cans('view_reports'), 403);

        $data = $this->getRequerimientosPorCubrirData($request);
        $title = 'Requerimientos por Cubrir - ' . Carbon::now()->format('d-m-Y');

        $pdf = Pdf::loadView('admin.reports.pdf.requerimientos-por-cubrir', compact('data', 'title'))
                  ->setPaper('a4', 'landscape');

        return $pdf->download($title . '.pdf');
    }

    public function exportHistoricoCanceladosPdf(Request $request)
    {
        abort_if(!$this->user->cans('view_reports'), 403);

        $data = $this->getHistoricoCanceladosData($request);
        $title = 'Base Histórica de Requerimientos Cancelados - ' . Carbon::now()->format('d-m-Y');

        $pdf = Pdf::loadView('admin.reports.pdf.historico-cancelados', compact('data', 'title'))
                  ->setPaper('a4', 'landscape');

        return $pdf->download($title . '.pdf');
    }

    public function exportCandidatosNoAptosPdf(Request $request)
    {
        abort_if(!$this->user->cans('view_reports'), 403);

        $data = $this->getCandidatosNoAptosData($request);
        $title = 'Base Histórica de Candidatos No Aptos - ' . Carbon::now()->format('d-m-Y');

        $pdf = Pdf::loadView('admin.reports.pdf.candidatos-no-aptos', compact('data', 'title'))
                  ->setPaper('a4', 'landscape');

        return $pdf->download($title . '.pdf');
    }

    public function exportBaseGeneralCandidatosPdf(Request $request)
    {
        abort_if(!$this->user->cans('view_reports'), 403);

        $data = $this->getBaseGeneralCandidatosData($request);
        $title = 'Base General de Candidatos - ' . Carbon::now()->format('d-m-Y');

        $pdf = Pdf::loadView('admin.reports.pdf.base-general-candidatos', compact('data', 'title'))
                  ->setPaper('a4', 'landscape');

        return $pdf->download($title . '.pdf');
    }

    /**
     * MÉTODOS AUXILIARES PARA OBTENER DATOS SIN PAGINACIÓN
     */
    private function getCandidatosEnProcesoData(Request $request)
    {
        $estadosEnProceso = ApplicationStatus::whereNotIn('status', ['contratado', 'rechazado'])->pluck('id');

        $query = JobApplication::select([
                'job_applications.id',
                'job_applications.full_name',
                'job_applications.email',
                'job_applications.phone',
                'job_applications.gender',
                'job_applications.dob',
                'job_applications.address',
                'job_applications.created_at',
                'job_applications.updated_at',
                'jobs.title as puesto',
                'job_descriptions.name as descripcion_puesto',
                'job_categories.name as categoria_puesto',
                'cost_centers.name as centro_costo',
                'cost_centers.code as codigo_centro_costo',
                'job_locations.location as unidad_minera',
                'application_status.status as etapa_actual',
                'application_status.title as titulo_etapa',
                'jobs.starting_salary',
                'jobs.maximum_salary',
                'jobs.pay_type',
                'users.name as responsable_proceso'
            ])
            ->join('jobs', 'job_applications.job_id', '=', 'jobs.id')
            ->leftJoin('job_descriptions', 'jobs.job_description_id', '=', 'job_descriptions.id')
            ->leftJoin('job_categories', 'jobs.category_id', '=', 'job_categories.id')
            ->leftJoin('cost_centers', 'jobs.cost_center_id', '=', 'cost_centers.id')
            ->leftJoin('job_job_locations', 'jobs.id', '=', 'job_job_locations.job_id')
            ->leftJoin('job_locations', 'job_job_locations.location_id', '=', 'job_locations.id')
            ->leftJoin('application_status', 'job_applications.status_id', '=', 'application_status.id')
            ->leftJoin('users', 'jobs.created_by_user_id', '=', 'users.id')
            ->whereIn('job_applications.status_id', $estadosEnProceso)
            ->whereNull('job_applications.deleted_at');

        if ($request->filled('start_date')) {
            $query->whereDate('job_applications.created_at', '>=', $request->start_date);
        }

        if ($request->filled('end_date')) {
            $query->whereDate('job_applications.created_at', '<=', $request->end_date);
        }

        return $query->groupBy('job_applications.id')->get();
    }

    private function getHistoricoContratadosData(Request $request)
    {
        $estadoContratado = ApplicationStatus::where('status', 'contratado')->first();

        $query = JobApplication::select([
                'job_applications.id',
                'job_applications.full_name',
                'job_applications.email',
                'job_applications.phone',
                'job_applications.gender',
                'job_applications.dob',
                'job_applications.address',
                'job_applications.created_at',
                'job_applications.updated_at',
                'jobs.title as puesto',
                'job_descriptions.name as descripcion_puesto',
                'job_categories.name as categoria_puesto',
                'cost_centers.name as centro_costo',
                'cost_centers.code as codigo_centro_costo',
                'job_locations.location as unidad_minera',
                'jobs.starting_salary',
                'jobs.maximum_salary',
                'jobs.pay_type',
                'users.name as responsable_proceso',
                'on_board_details.joining_date as fecha_subida',
                'on_board_details.salary_offered'
            ])
            ->join('jobs', 'job_applications.job_id', '=', 'jobs.id')
            ->leftJoin('job_descriptions', 'jobs.job_description_id', '=', 'job_descriptions.id')
            ->leftJoin('job_categories', 'jobs.category_id', '=', 'job_categories.id')
            ->leftJoin('cost_centers', 'jobs.cost_center_id', '=', 'cost_centers.id')
            ->leftJoin('job_job_locations', 'jobs.id', '=', 'job_job_locations.job_id')
            ->leftJoin('job_locations', 'job_job_locations.location_id', '=', 'job_locations.id')
            ->leftJoin('users', 'jobs.created_by_user_id', '=', 'users.id')
            ->leftJoin('on_board_details', 'job_applications.id', '=', 'on_board_details.job_application_id')
            ->where('job_applications.status_id', $estadoContratado->id)
            ->whereNull('job_applications.deleted_at');

        if ($request->filled('start_date')) {
            $query->whereDate('on_board_details.joining_date', '>=', $request->start_date);
        }

        if ($request->filled('end_date')) {
            $query->whereDate('on_board_details.joining_date', '<=', $request->end_date);
        }

        return $query->groupBy('job_applications.id')->get();
    }

    private function getRequerimientosPorCubrirData(Request $request)
    {
        $query = Job::select([
                'jobs.id',
                'jobs.code',
                'jobs.title as puesto',
                'job_descriptions.name as descripcion_puesto',
                'jobs.total_positions',
                'jobs.start_date',
                'jobs.created_at',
                'job_categories.name as categoria_puesto',
                'cost_centers.name as centro_costo',
                'cost_centers.code as codigo_centro_costo',
                'job_locations.location as unidad_minera'
            ])
            ->leftJoin('job_descriptions', 'jobs.job_description_id', '=', 'job_descriptions.id')
            ->leftJoin('job_categories', 'jobs.category_id', '=', 'job_categories.id')
            ->leftJoin('cost_centers', 'jobs.cost_center_id', '=', 'cost_centers.id')
            ->leftJoin('job_job_locations', 'jobs.id', '=', 'job_job_locations.job_id')
            ->leftJoin('job_locations', 'job_job_locations.location_id', '=', 'job_locations.id')
            ->where('jobs.status', 'active');

        if ($request->filled('start_date')) {
            $query->whereDate('jobs.start_date', '>=', $request->start_date);
        }

        if ($request->filled('end_date')) {
            $query->whereDate('jobs.start_date', '<=', $request->end_date);
        }

        return $query->groupBy('jobs.id')->get();
    }

    private function getHistoricoCanceladosData(Request $request)
    {
        $query = Job::select([
                'jobs.id',
                'jobs.code',
                'jobs.title as puesto',
                'job_descriptions.name as descripcion_puesto',
                'jobs.total_positions',
                'jobs.updated_at',
                'jobs.canceled_reason',
                'job_categories.name as categoria_puesto',
                'cost_centers.name as centro_costo',
                'cost_centers.code as codigo_centro_costo',
                'job_locations.location as unidad_minera'
            ])
            ->leftJoin('job_descriptions', 'jobs.job_description_id', '=', 'job_descriptions.id')
            ->leftJoin('job_categories', 'jobs.category_id', '=', 'job_categories.id')
            ->leftJoin('cost_centers', 'jobs.cost_center_id', '=', 'cost_centers.id')
            ->leftJoin('job_job_locations', 'jobs.id', '=', 'job_job_locations.job_id')
            ->leftJoin('job_locations', 'job_job_locations.location_id', '=', 'job_locations.id')
            ->where('jobs.status', 'canceled');

        if ($request->filled('start_date')) {
            $query->whereDate('jobs.updated_at', '>=', $request->start_date);
        }

        if ($request->filled('end_date')) {
            $query->whereDate('jobs.updated_at', '<=', $request->end_date);
        }

        return $query->groupBy('jobs.id')->get();
    }

    private function getCandidatosNoAptosData(Request $request)
    {
        $estadoRechazado = ApplicationStatus::where('status', 'rechazado')->first();

        $query = JobApplication::select([
                'job_applications.id',
                'job_applications.full_name',
                'job_applications.email',
                'job_applications.phone',
                'job_applications.gender',
                'job_applications.dob',
                'job_applications.address',
                'job_applications.updated_at',
                'jobs.title as puesto',
                'job_descriptions.name as descripcion_puesto',
                'job_categories.name as categoria_puesto',
                'cost_centers.name as centro_costo',
                'cost_centers.code as codigo_centro_costo',
                'job_locations.location as unidad_minera'
            ])
            ->join('jobs', 'job_applications.job_id', '=', 'jobs.id')
            ->leftJoin('job_descriptions', 'jobs.job_description_id', '=', 'job_descriptions.id')
            ->leftJoin('job_categories', 'jobs.category_id', '=', 'job_categories.id')
            ->leftJoin('cost_centers', 'jobs.cost_center_id', '=', 'cost_centers.id')
            ->leftJoin('job_job_locations', 'jobs.id', '=', 'job_job_locations.job_id')
            ->leftJoin('job_locations', 'job_job_locations.location_id', '=', 'job_locations.id')
            ->where('job_applications.status_id', $estadoRechazado->id)
            ->whereNull('job_applications.deleted_at');

        if ($request->filled('start_date')) {
            $query->whereDate('job_applications.updated_at', '>=', $request->start_date);
        }

        if ($request->filled('end_date')) {
            $query->whereDate('job_applications.updated_at', '<=', $request->end_date);
        }

        return $query->groupBy('job_applications.id')->get();
    }

    private function getBaseGeneralCandidatosData(Request $request)
    {
        $query = JobApplication::select([
                'job_applications.id',
                'job_applications.full_name',
                'job_applications.email',
                'job_applications.phone',
                'job_applications.gender',
                'job_applications.dob',
                'job_applications.address',
                'job_applications.created_at',
                'job_applications.updated_at',
                'jobs.title as puesto',
                'job_descriptions.name as descripcion_puesto',
                'job_categories.name as categoria_puesto',
                'cost_centers.name as centro_costo',
                'cost_centers.code as codigo_centro_costo',
                'job_locations.location as unidad_minera',
                'application_status.status as etapa_actual',
                'application_status.title as titulo_etapa',
                'users.name as responsable_proceso'
            ])
            ->join('jobs', 'job_applications.job_id', '=', 'jobs.id')
            ->leftJoin('job_descriptions', 'jobs.job_description_id', '=', 'job_descriptions.id')
            ->leftJoin('job_categories', 'jobs.category_id', '=', 'job_categories.id')
            ->leftJoin('cost_centers', 'jobs.cost_center_id', '=', 'cost_centers.id')
            ->leftJoin('job_job_locations', 'jobs.id', '=', 'job_job_locations.job_id')
            ->leftJoin('job_locations', 'job_job_locations.location_id', '=', 'job_locations.id')
            ->leftJoin('application_status', 'job_applications.status_id', '=', 'application_status.id')
            ->leftJoin('users', 'jobs.created_by_user_id', '=', 'users.id')
            ->whereNull('job_applications.deleted_at');

        if ($request->filled('start_date')) {
            $query->whereDate('job_applications.created_at', '>=', $request->start_date);
        }

        if ($request->filled('end_date')) {
            $query->whereDate('job_applications.created_at', '<=', $request->end_date);
        }

        return $query->groupBy('job_applications.id')->get();
    }
}
