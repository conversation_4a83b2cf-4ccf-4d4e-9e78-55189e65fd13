<?php

use App\Http\Controllers\Admin\AdminJobTypeController;
use App\Http\Controllers\Admin\AdminCurrencyController;
use App\Http\Controllers\Admin\AdminSecurityController;
use App\Http\Controllers\Admin\AdminWorkExperienceController;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!
|
*/
Route::post('/zoom-webhook', 'ZoomWebhookController@index')->name('zoom-webhook');
//Front routes start
// Admin routes
Route::group(
    ['namespace' => 'Front', 'as' => 'jobs.'],
    function () {
        Route::get('/', 'FrontJobsController@jobOpenings')->name('jobOpenings')->Middleware('disable-frontend');
        Route::post('/more-data', 'FrontJobsController@moreData')->name('more-data');
        Route::post('/search-job', 'FrontJobsController@searchJob')->name('search-job');
        Route::get('/job-offer/{slug?}', 'FrontJobOfferController@index')->name('job-offer');
        Route::post('/save-offer', 'FrontJobOfferController@saveOffer')->name('save-offer');
        Route::get('/job/{slug}/{location?}/{hash?}', 'FrontJobsController@jobDetail')->name('jobDetail');
        Route::get('/jobapply/{slug}/{location?}/{hash?}', 'FrontJobsController@jobApply')->name('jobApply');
        Route::post('/job/saveApplication', 'FrontJobsController@saveApplication')->name('saveApplication');
        Route::post('/job/fetch-country-state', 'FrontJobsController@fetchCountryState')->name('fetchCountryState');
        Route::post('change-language/{code}', 'FrontJobsController@changeLanguage')->name('changeLanguage');
        Route::get('auth/callback/{provider}', 'FrontJobsController@callback')->name('linkedinCallback');
        Route::get('auth/redirect/{provider}', 'FrontJobsController@redirect')->name('linkedinRedirect');
        Route::get('job-alert', 'FrontJobsController@jobAlert')->name('jobAlert');
        Route::post('save-job-alert', 'FrontJobsController@saveJobAlert')->name('saveJobAlert');
        Route::Post('disable-job-alert/{id}', 'FrontJobsController@disableJobAlert')->name('disableJobAlert');
    }
);

//Front routes end

Auth::routes();

Route::group(['middleware' => 'auth'], function () {

    Route::post('mark-notification-read', ['uses' => 'NotificationController@markAllRead'])->name('mark-notification-read');
    Route::post('mark-read', 'NotificationController@markRead')->name('mark_single_notification_read');

    // Admin routes
    Route::group(
        ['namespace' => 'Admin', 'prefix' => 'admin', 'as' => 'admin.'],
        function () {
            Route::get('/dashboard', 'AdminDashboardController@index')->name('dashboard');

            Route::get('job-categories/data', 'AdminJobCategoryController@data')->name('job-categories.data');
            Route::get('job-categories/getSkills/{categoryId}', 'AdminJobCategoryController@getSkills')->name('job-categories.getSkills');
            Route::resource('job-categories', 'AdminJobCategoryController');

            //Questions
            Route::get('questions/data', 'AdminQuestionController@data')->name('questions.data');
            Route::resource('questions', 'AdminQuestionController');

            Route::post('todo-items/update-todo-item', 'AdminTodoItemController@updateTodoItem')->name('todo-items.updateTodoItem');
            Route::resource('todo-items', 'AdminTodoItemController');
            Route::get('job_alert/data', 'AdminJobAlertController@data')->name('job_alert.data');
            Route::resource('job_alert', 'AdminJobAlertController', ['only' => ['index', 'destroy']]);

            // company settings
            Route::group(
                ['prefix' => 'settings'],
                function () {
                    // Company Setting routes
                    Route::resource('settings', 'CompanySettingsController', ['only' => ['edit', 'update', 'index']]);

                    // Application Form routes
                    Route::resource('application-setting', 'ApplicationSettingsController');

                    // Role permission routes
                    Route::post('role-permission/assignAllPermission', ['as' => 'role-permission.assignAllPermission', 'uses' => 'ManageRolePermissionController@assignAllPermission']);
                    Route::post('role-permission/removeAllPermission', ['as' => 'role-permission.removeAllPermission', 'uses' => 'ManageRolePermissionController@removeAllPermission']);
                    Route::post('role-permission/assignRole', ['as' => 'role-permission.assignRole', 'uses' => 'ManageRolePermissionController@assignRole']);
                    Route::post('role-permission/detachRole', ['as' => 'role-permission.detachRole', 'uses' => 'ManageRolePermissionController@detachRole']);
                    Route::post('role-permission/storeRole', ['as' => 'role-permission.storeRole', 'uses' => 'ManageRolePermissionController@storeRole']);
                    Route::post('role-permission/deleteRole', ['as' => 'role-permission.deleteRole', 'uses' => 'ManageRolePermissionController@deleteRole']);
                    Route::get('role-permission/showMembers/{id}', ['as' => 'role-permission.showMembers', 'uses' => 'ManageRolePermissionController@showMembers']);
                    Route::resource('role-permission', 'ManageRolePermissionController');

                    //language settings
                    Route::post('language-settings/change-language', ['uses' => 'LanguageSettingsController@changeLanguage'])->name('language-settings.change-language');
                    Route::put('language-settings/change-language-status/{id}', 'LanguageSettingsController@changeStatus')->name('language-settings.changeStatus');

                    Route::resource('language-settings', 'LanguageSettingsController');

                    Route::resource('theme-settings', 'AdminThemeSettingsController');
                    Route::post('theme-settings/disable-frontend', ['uses' => 'AdminThemeSettingsController@disableFrontend'])->name('theme-settings.disableFrontend');

                    Route::get('smtp-settings/sent-test-email', ['uses' => 'AdminSmtpSettingController@sendTestEmail'])->name('smtp-settings.sendTestEmail');
                    Route::resource('smtp-settings', 'AdminSmtpSettingController');

                    Route::resource('sms-settings', 'AdminSmsSettingsController', ['only' => ['index', 'update']]);

                    Route::resource('linkedin-settings', 'AdminLinkedInSettingsController');

                    // Footer Links
                    Route::get('footer-settings/data', 'FooterSettingController@data')->name('footer-settings.data');
                    Route::resource('footer-settings', 'FooterSettingController');

                    Route::get('update-application', ['uses' => 'UpdateApplicationController@index'])->name('update-application.index');
                }
            );
            //zoom
            Route::get('zoom-meeting/table', 'AdminZoomMeetingController@tableView')->name('zoom-meeting.table-view');
            Route::get('zoom-meeting/data', 'AdminZoomMeetingController@data')->name('zoom-meeting.data');
            Route::get('zoom-meeting/start-meeting/{id}', 'AdminZoomMeetingController@startMeeting')->name('zoom-meeting.startMeeting');
            Route::post('zoom-meeting/cancel-meeting', 'AdminZoomMeetingController@cancelMeeting')->name('zoom-meeting.cancelMeeting');
            Route::post('zoom-meeting/end-meeting', 'AdminZoomMeetingController@endMeeting')->name('zoom-meeting.endMeeting');
            Route::post('zoom-meeting/updateOccurrence/{id}', 'AdminZoomMeetingController@updateOccurrence')->name('zoom-meeting.updateOccurrence');
            Route::resource('zoom-meeting', 'AdminZoomMeetingController');
            Route::resource('category', 'CategoryController');
            Route::resource('zoom-setting', 'ZoomMeetingSettingController');
            Route::post('zoom-setting/change-status/{id}', 'ZoomMeetingSettingController@changeStatus')->name('zoom-setting.change-status');
            Route::get('skills/data', 'AdminSkillsController@data')->name('skills.data');
            Route::resource('skills', 'AdminSkillsController');

            Route::get('locations/data', 'AdminLocationsController@data')->name('locations.data');
            Route::resource('locations', 'AdminLocationsController');

            Route::get('jobs/data', 'AdminJobsController@data')->name('jobs.data');

            Route::post('jobs/refresh-date', 'AdminJobsController@refreshDate')->name('jobs.refreshDate');

            Route::get('jobs/application-data', 'AdminJobsController@applicationData')->name('jobs.applicationData');
            Route::post('jobs/send-emails', 'AdminJobsController@sendEmails')->name('jobs.sendEmails');
            Route::get('jobs/send-email', 'AdminJobsController@sendEmail')->name('jobs.sendEmail');

            Route::resource('jobs', 'AdminJobsController');

            Route::post('job-applications/rating-save/{id?}', 'AdminJobApplicationController@ratingSave')->name('job-applications.rating-save');
            Route::get('job-applications/create-schedule/{id?}', 'AdminJobApplicationController@createSchedule')->name('job-applications.create-schedule');
            Route::post('job-applications/store-schedule', 'AdminJobApplicationController@storeSchedule')->name('job-applications.store-schedule');
            Route::get('job-applications/question/{jobID}/{applicationId?}', 'AdminJobApplicationController@jobQuestion')->name('job-applications.question');
            Route::get('job-applications/export/{status}/{location}/{startDate}/{endDate}/{jobs}', 'AdminJobApplicationController@export')->name('job-applications.export');
            Route::get('job-applications/data', 'AdminJobApplicationController@data')->name('job-applications.data');
            Route::get('job-applications/load-more', 'AdminJobApplicationController@loadMore')->name('job-applications.loadMore');
            Route::get('job-applications/table-view', 'AdminJobApplicationController@table')->name('job-applications.table');
            Route::post('job-applications/updateIndex', 'AdminJobApplicationController@updateIndex')->name('job-applications.updateIndex');
            Route::post('job-applications/archive-job-application/{application}', 'AdminJobApplicationController@archiveJobApplication')->name('job-applications.archiveJobApplication');
            Route::post('job-applications/reject-job-application/{application}', 'AdminJobApplicationController@rejectJobApplication')->name('job-applications.rejectJobApplication');
            Route::post('job-applications/unarchive-job-application/{application}', 'AdminJobApplicationController@unarchiveJobApplication')->name('job-applications.unarchiveJobApplication');
            Route::post('job-applications/add-skills/{applicationId}', 'AdminJobApplicationController@addSkills')->name('job-applications.addSkills');
            Route::get('job-applications/get-jobs', 'AdminJobApplicationController@getJobs')->name('job-applications.get-jobs');
            Route::post('job-applications/update-documentation/{application}', 'AdminJobApplicationController@updateDocumentation')->name('job-applications.updateDocumentation');
            Route::get('job-applications/show-upload-required-document-form/{application}/{documentation}', 'AdminJobApplicationController@showUploadRequiredDocumentForm')->name('job-applications.showUploadRequiredDocumentForm');
            Route::post('job-applications/upload-required-document/{application}', 'AdminJobApplicationController@uploadRequiredDocument')->name('job-applications.uploadRequiredDocument');
            Route::delete('job-applications/delete-required-document/{application}/{documentation}', 'AdminJobApplicationController@deleteRequiredDocument')->name('job-applications.deleteRequiredDocument');
            Route::resource('job-applications', 'AdminJobApplicationController');

            Route::get('applications-archive/data', 'AdminApplicationArchiveController@data')->name('applications-archive.data');
            Route::get('applications-archive/export/{skill}', 'AdminApplicationArchiveController@export')->name('applications-archive.export');
            Route::resource('applications-archive', 'AdminApplicationArchiveController');
            Route::post('applications-archive{id}', 'AdminApplicationArchiveController@deleteRecords')->name('applications-archive.deleteRecords');

            Route::get('job-onboard/data', 'AdminJobOnboardController@data')->name('job-onboard.data');
            Route::get('job-onboard/data/{id}', 'AdminJobOnboardController@dataByCandidate')->name('job-onboard.data-by-candidate');
            Route::get('job-onboard/send-offer/{id?}', 'AdminJobOnboardController@sendOffer')->name('job-onboard.send-offer');
            Route::get('job-onboard/update-status/{id?}', 'AdminJobOnboardController@updateStatus')->name('job-onboard.update-status');
            Route::resource('job-onboard', 'AdminJobOnboardController');
            Route::get('job-onboard-questions/data', 'AdminJobOfferQuestionController@data')->name('job-onboard-questions.data');

            Route::resource('job-onboard-questions', 'AdminJobOfferQuestionController');

            Route::resource('profile', 'AdminProfileController');
            Route::resource('application-status', 'AdminApplicationStatusController');

            Route::get('interview-schedule/data', 'InterviewScheduleController@data')->name('interview-schedule.data');
            Route::get('interview-schedule/data/{id}', 'InterviewScheduleController@dataByCandidate')->name('interview-schedule.data-by-candidate');
            Route::get('interview-schedule/table-view', 'InterviewScheduleController@table')->name('interview-schedule.table-view');
            Route::post('interview-schedule/change-status', 'InterviewScheduleController@changeStatus')->name('interview-schedule.change-status');
            Route::post('interview-schedule/change-status-multiple', 'InterviewScheduleController@changeStatusMultiple')->name('interview-schedule.change-status-multiple');
            Route::get('interview-schedule/notify/{id}/{type}', 'InterviewScheduleController@notify')->name('interview-schedule.notify');
            Route::get('interview-schedule/response/{id}/{type}', 'InterviewScheduleController@employeeResponse')->name('interview-schedule.response');
            Route::resource('interview-schedule', 'InterviewScheduleController');

            Route::get('team/data', 'AdminTeamController@data')->name('team.data');
            Route::post('team/change-role', 'AdminTeamController@changeRole')->name('team.changeRole');
            Route::resource('team', 'AdminTeamController');

            Route::get('empleopolis/data', 'AdminEmpleopolisController@data')->name('empleopolis.data');
            Route::get('empleopolis/data/{id}', 'AdminEmpleopolisController@dataByCandidate')->name('empleopolis.data-by-candidate');
            Route::resource('empleopolis', 'AdminEmpleopolisController');

            Route::get('adjunct/data', 'AdminAdjunctController@data')->name('adjunct.data');
            Route::get('adjunct/data/{id}', 'AdminAdjunctController@dataByCandidate')->name('adjunct.data-by-candidate');
            Route::resource('adjunct', 'AdminAdjunctController');

            Route::get('signus/data', 'AdminSignusController@data')->name('signus.data');
            Route::get('signus/data/{id}', 'AdminSignusController@dataByCandidate')->name('signus.data-by-candidate');
            Route::resource('signus', 'AdminSignusController');

            Route::get('medical-exam/data', 'AdminMedicalExamController@data')->name('medical-exam.data');
            Route::get('medical-exam/data/{id}', 'AdminMedicalExamController@dataByCandidate')->name('medical-exam.data-by-candidate');
            Route::resource('medical-exam', 'AdminMedicalExamController');

            Route::get('company/data', 'AdminCompanyController@data')->name('company.data');
            // Route::get('compant/show', 'AdminCompanyController@show')->name('company.show');
            Route::resource('company', 'AdminCompanyController');
            Route::resource('currency-settings', 'AdminCurrencyController');
            Route::get('/security-setting/verifyCaptcha', 'AdminSecurityController@verifyCaptcha')->name('security-setting.verifyCaptcha');
            Route::resource('/security-setting', 'AdminSecurityController', ['only' => ['index', 'update']]);
            Route::resource('storage-settings', 'StorageController');
            Route::resource('applicant-note', 'ApplicantNoteController');
            Route::resource('sticky-note', 'AdminStickyNotesController');
            Route::resource('departments', 'AdminDepartmentController');
            Route::resource('job-type', 'AdminJobTypeController');
            Route::resource('work-experience', 'AdminWorkExperienceController');
            Route::resource('designations', 'AdminDesignationController');
            Route::get('documents/data', 'AdminDocumentController@data')->name('documents.data');
            Route::get('documents/download-document/{document}', 'AdminDocumentController@downloadDoc')->name('documents.downloadDoc');
            Route::resource('documents', 'AdminDocumentController');
            Route::resource('report', 'AdminReportController');
            // Route::get('projects/data', 'AdminProjectController@data')->name('projects.data');
            // Route::resource('projects', 'AdminProjectController');
            Route::get('clinics/data', 'AdminClinicController@data')->name('clinics.data');
            Route::resource('clinics', 'AdminClinicController');
            Route::get('cost_centers/data', 'AdminCostCenterController@data')->name('cost_centers.data');
            Route::resource('cost_centers', 'AdminCostCenterController');
            Route::get('job_descriptions/data', 'AdminJobDescriptionController@data')->name('job_descriptions.data');
            Route::resource('job_descriptions', 'AdminJobDescriptionController');

            // Reportes
            Route::group(['prefix' => 'reports', 'as' => 'reports.'], function () {
                Route::get('/', 'AdminReportsController@index')->name('index');

                // Reporte 1: Candidatos en Proceso
                Route::get('/candidatos-en-proceso', 'AdminReportsController@candidatosEnProceso')->name('candidatos-en-proceso');
                Route::get('/candidatos-en-proceso/data', 'AdminReportsController@candidatosEnProcesoData')->name('candidatos-en-proceso.data');

                // Reporte 2: Histórico de Contratados
                Route::get('/historico-contratados', 'AdminReportsController@historicoContratados')->name('historico-contratados');
                Route::get('/historico-contratados/data', 'AdminReportsController@historicoContratadosData')->name('historico-contratados.data');

                // Reporte 3: Requerimientos por Cubrir
                Route::get('/requerimientos-por-cubrir', 'AdminReportsController@requerimientosPorCubrir')->name('requerimientos-por-cubrir');
                Route::get('/requerimientos-por-cubrir/data', 'AdminReportsController@requerimientosPorCubrirData')->name('requerimientos-por-cubrir.data');

                // Reporte 4: Histórico de Cancelados
                Route::get('/historico-cancelados', 'AdminReportsController@historicoCancelados')->name('historico-cancelados');
                Route::get('/historico-cancelados/data', 'AdminReportsController@historicoCanceladosData')->name('historico-cancelados.data');

                // Reporte 5: Candidatos No Aptos
                Route::get('/candidatos-no-aptos', 'AdminReportsController@candidatosNoAptos')->name('candidatos-no-aptos');
                Route::get('/candidatos-no-aptos/data', 'AdminReportsController@candidatosNoAptosData')->name('candidatos-no-aptos.data');

                // Reporte 6: Base General de Candidatos
                Route::get('/base-general-candidatos', 'AdminReportsController@baseGeneralCandidatos')->name('base-general-candidatos');
                Route::get('/base-general-candidatos/data', 'AdminReportsController@baseGeneralCandidatosData')->name('base-general-candidatos.data');

                // Exportaciones Excel
                Route::get('/candidatos-en-proceso/export', 'AdminReportsController@exportCandidatosEnProceso')->name('candidatos-en-proceso.export');
                Route::get('/historico-contratados/export', 'AdminReportsController@exportHistoricoContratados')->name('historico-contratados.export');
                Route::get('/requerimientos-por-cubrir/export', 'AdminReportsController@exportRequerimientosPorCubrir')->name('requerimientos-por-cubrir.export');
                Route::get('/historico-cancelados/export', 'AdminReportsController@exportHistoricoCancelados')->name('historico-cancelados.export');
                Route::get('/candidatos-no-aptos/export', 'AdminReportsController@exportCandidatosNoAptos')->name('candidatos-no-aptos.export');
                Route::get('/base-general-candidatos/export', 'AdminReportsController@exportBaseGeneralCandidatos')->name('base-general-candidatos.export');

                // Exportaciones PDF
                Route::get('/candidatos-en-proceso/export-pdf', 'AdminReportsController@exportCandidatosEnProcesoPdf')->name('candidatos-en-proceso.export-pdf');
                Route::get('/historico-contratados/export-pdf', 'AdminReportsController@exportHistoricoContratadosPdf')->name('historico-contratados.export-pdf');
                Route::get('/requerimientos-por-cubrir/export-pdf', 'AdminReportsController@exportRequerimientosPorCubrirPdf')->name('requerimientos-por-cubrir.export-pdf');
                Route::get('/historico-cancelados/export-pdf', 'AdminReportsController@exportHistoricoCanceladosPdf')->name('historico-cancelados.export-pdf');
                Route::get('/candidatos-no-aptos/export-pdf', 'AdminReportsController@exportCandidatosNoAptosPdf')->name('candidatos-no-aptos.export-pdf');
                Route::get('/base-general-candidatos/export-pdf', 'AdminReportsController@exportBaseGeneralCandidatosPdf')->name('base-general-candidatos.export-pdf');
            });
        }
    );

    Route::get('change-mobile', 'VerifyMobileController@changeMobile')->name('changeMobile');
    Route::post('send-otp-code', 'VerifyMobileController@sendVerificationCode')->name('sendOtpCode');
    Route::post('send-otp-code/account', 'VerifyMobileController@sendVerificationCode')->name('sendOtpCode.account');
    Route::post('verify-otp-phone', 'VerifyMobileController@verifyOtpCode')->name('verifyOtpCode');
    Route::post('verify-otp-phone/account', 'VerifyMobileController@verifyOtpCode')->name('verifyOtpCode.account');
    Route::get('remove-session', 'VerifyMobileController@removeSession')->name('removeSession');
});

Route::group(
    ['namespace' => 'Front', 'as' => 'jobs.'],
    function () {
        Route::get('{slug}', 'FrontJobsController@customPage')->name('custom-page');
    }
);
