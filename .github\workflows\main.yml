name: Deploy Seprocal
on:
  push:
    branches:
      - main
  pull_request:
    branches:
      - main


jobs:
  purge-old-files:
    if: github.ref_name == 'main'
    runs-on: ubuntu-latest
    steps:
      - uses: appleboy/ssh-action@dce9d565de8d876c11d93fa4fe677c0285a66d78
        with:
          host: *************
          username: root
          key: ${{ secrets.KEY }}
          port: 22
          script: |
            cd /var/www/seprocal-ats-webapp/  # Ensure correct path
            rm -rf app
            rm -rf database
            # Add other cleanup commands as needed

  copy-files:
    if: github.ref_name == 'main'
    needs: purge-old-files
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3 # Use v3 or latest
      - name: Create target directory (if it doesn't exist)
        uses: appleboy/ssh-action@dce9d565de8d876c11d93fa4fe677c0285a66d78
        with:
          host: *************
          username: root
          key: ${{ secrets.KEY }}
          port: 22
          script: |
            mkdir -p /var/www/seprocal-ats-webapp/
            chown root:root /var/www/seprocal-ats-webapp/ # Ensure correct ownership
      - name: Rsync files to server
        uses: appleboy/ssh-action@dce9d565de8d876c11d93fa4fe677c0285a66d78
        with:
          host: *************
          username: root
          key: ${{ secrets.KEY }}
          port: 22
          script: |
            rsync -avzhe 'ssh -p 22 -i ${{ secrets.KEY_PATH }}' . root@*************:/var/www/seprocal-ats-webapp/
      - name: Write SSH key to file
        run: echo "${{ secrets.KEY }}" > key.pem && chmod 600 key.pem
        shell: bash
      
      - name: Rsync files to server
        uses: appleboy/ssh-action@dce9d565de8d876c11d93fa4fe677c0285a66d78
        with:
          host: *************
          username: root
          key: ${{ secrets.KEY }} 
          port: 22
          script: |
            rsync -avzhe 'ssh -p 22 -i key.pem'. root@*************:/var/www/seprocal-ats-webapp/


  execute-deploy-commands:
    if: github.ref_name == 'main'
    needs: copy-files
    runs-on: ubuntu-latest
    steps:
      - name: Run deploy commands
        uses: appleboy/ssh-action@dce9d565de8d876c11d93fa4fe677c0285a66d78
        with:
          host: ${{ secrets.HOST }}
          username: ${{ secrets.USERNAME }}
          key: ${{ secrets.KEY }}
          port: ${{ secrets.PORT }}
          script: |
            cp /var/www/.seprocal.env ${{ secrets.PATH }}.env
            cd ${{ secrets.PATH }}
            composer install
            php artisan optimize:clear
            npm ci
            npm run build
