<?php

use App\Role;
use App\Module;
use App\Permission;
use Illuminate\Database\Migrations\Migration;

return new class () extends Migration {
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        // Crear módulo de reportes si no existe
        $module = Module::where('module_name', 'reports')->first();
        if (!$module) {
            // Obtener el próximo ID disponible
            $nextId = Module::max('id') + 1;
            Module::insert([
                ['id' => $nextId, 'module_name' => 'reports', 'description' => '']
            ]);
            $moduleId = $nextId;
        } else {
            $moduleId = $module->id;
        }

        $permissions = [
            ['name' => 'view_reports', 'display_name' => 'View Reports', 'module_id' => $moduleId],
            ['name' => 'export_reports', 'display_name' => 'Export Reports', 'module_id' => $moduleId],
        ];

        $admin = Role::where('name', 'admin')->first();

        foreach ($permissions as $permission) {
            // Verificar si el permiso ya existe
            $existingPermission = Permission::where('name', $permission['name'])->first();
            if (!$existingPermission) {
                $create = Permission::create($permission);
                if ($admin) {
                    $admin->attachPermission($create);
                }
            }
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Permission::whereIn('name', ['view_reports', 'export_reports'])->delete();
        Module::where('module_name', 'reports')->delete();
    }
};
