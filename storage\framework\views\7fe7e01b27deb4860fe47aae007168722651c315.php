 <?php $__env->startPush('head-script'); ?>
<link rel="stylesheet" href="//cdn.datatables.net/fixedheader/3.1.5/css/fixedHeader.bootstrap.min.css">
<link rel="stylesheet" href="//cdn.datatables.net/responsive/2.2.3/css/responsive.bootstrap.min.css">
<style>
    .mb-20 {
        margin-bottom: 20px
    }
    .badge-color{
        color: aliceblue;
    }
</style>


<?php $__env->stopPush(); ?> 
<?php $__env->startSection('content'); ?>
<div class="row">
    <div class="col-md-3 col-sm-6 col-12">
        <div class="info-box">
            <span class="info-box-icon" style="background-color: #95a5a6;"><i class="icon-badge badge-color"></i></span>

            <div class="info-box-content">
                <span class="info-box-text"><?php echo app('translator')->get('modules.report.jobapplication'); ?></span>
                <span class="info-box-number"><?php echo e($jobApplication); ?></span>
            </div>
            <!-- /.info-box-content -->
        </div>
        <!-- /.info-box -->
    </div>

    <div class="col-md-3 col-sm-6 col-12">
        <div class="info-box">
            <span class="info-box-icon" style="background-color: #9b59b6;"><i class="icon-badge badge-color"></i></span>

            <div class="info-box-content">
                <span class="info-box-text"><?php echo app('translator')->get('modules.report.job'); ?></span>
                <span class="info-box-number"><?php echo e($job); ?></span>
            </div>
            <!-- /.info-box-content -->
        </div>
        <!-- /.info-box -->
    </div>

    <div class="col-md-3 col-sm-6 col-12">
        <div class="info-box">
            <span class="info-box-icon" style="background-color: #28a745;"><i class="icon-badge badge-color"></i></span>

            <div class="info-box-content">
                <span class="info-box-text"><?php echo app('translator')->get('modules.report.candidatehired'); ?></span>
                <span class="info-box-number"><?php echo e($candidatesHired); ?></span>
            </div>
            <!-- /.info-box-content -->
        </div>
        <!-- /.info-box -->
    </div>
    <div class="col-md-3 col-sm-6 col-12">
        <div class="info-box">
            <span class="info-box-icon" style="background-color: #3D8EE8;"><i class="icon-badge badge-color"></i></span>

            <div class="info-box-content">
                <span class="info-box-text"><?php echo app('translator')->get('modules.report.interviewschedule'); ?></span>
                <span class="info-box-number"><?php echo e($interviewScheduled); ?></span>
            </div>
            <!-- /.info-box-content -->
        </div>
        <!-- /.info-box -->
    </div>
</div>
<div class="row">
    <div class="col-md-3">
        <div class="form-group">
           
        </div>
    </div>
    

</div>
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <div class="row clearfix">
                    <div class="col-md-12 mb-20" id="">
                    <h3><?php echo app('translator')->get('modules.report.jobapplicationstatus'); ?></h3>
                    <canvas id="myChart" width="" height="">
                       
                    </div>
                </div>

                
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>
 <?php $__env->startPush('footer-script'); ?>
<script src="//cdn.datatables.net/fixedheader/3.1.5/js/dataTables.fixedHeader.min.js"></script>
<script src="//cdn.datatables.net/responsive/2.2.3/js/dataTables.responsive.min.js"></script>
<script src="//cdn.datatables.net/responsive/2.2.3/js/responsive.bootstrap.min.js"></script>
 <script src="<?php echo e(asset('assets/plugins/chart.js/Chart.min.js')); ?>"></script>
 <script>
 //for pie chart
  var chart = document.getElementById("myChart").getContext('2d');
  var cData = JSON.parse(`<?php echo $chart_data; ?>`);
  var keys = [];
  var value = [];
  $.each(cData, function(k, v) {
    value.push(v)
    keys.push(k)
});
  var myChart = new Chart(chart, {
  type: 'pie',
  data: {
    labels: keys,
    datasets: [{
      backgroundColor: [
       
        "#95a5a6",
        "#9b59b6",
        "#28a745",
        "#3D8EE8",
      ],
      data:value,
    }]
  }
});
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH E:\laragon\www\recruit-seprocal\resources\views/admin/report/index.blade.php ENDPATH**/ ?>